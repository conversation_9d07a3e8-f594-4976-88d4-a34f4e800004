/// <reference types="vite/client" />

declare module "*.vue" {
  import type { DefineComponent } from "vue";
  const component: DefineComponent<{}, {}, any>;
  export default component;
}

interface ImportMetaEnv {
  readonly VITE_API_BASE_URL: string
  readonly VITE_WS_URL: string
  readonly VITE_REQUEST_TIMEOUT: string
  readonly VITE_DEBUG: string
  readonly VITE_AUTO_LOGIN: string
  readonly VITE_AUTO_ANSWER: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
