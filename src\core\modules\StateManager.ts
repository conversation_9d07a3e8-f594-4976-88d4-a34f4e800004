/**
 * 状态管理模块
 * 负责管理座席状态
 */

import { IStateManager, IEventManager } from '../interfaces';
import { AgentStateType, CCBarResponse, WorkModeType } from '../../types';
import { RequestService } from '../../utils/request';
import { ccbarDebugger } from '../../utils';

/**
 * 状态管理器实现类
 */
export class StateManager implements IStateManager {
  private requestService: RequestService;
  private eventManager: IEventManager;
  private agentId: string;
  private currentState: AgentStateType;
  private config: any;

  /**
   * 构造函数
   * @param requestService HTTP请求服务
   * @param eventManager 事件管理器
   * @param config 配置信息
   */
  constructor(
    requestService: RequestService,
    eventManager: IEventManager,
    config: any
  ) {
    this.requestService = requestService;
    this.eventManager = eventManager;
    this.config = config;
    this.agentId = localStorage.getItem('agentId') || '';
    this.currentState = AgentStateType.LOGOFF;
  }

  /**
   * 更新会话ID和座席ID
   * @param sessionId 新的会话ID
   * @param agentId 座席ID
   * @deprecated sessionId在状态管理中不再使用，但为保持API兼容性而保留
   */
  public updateSessionId(sessionId: string, agentId: string): void {
    if (agentId) {
      this.agentId = agentId;
    }
    // sessionId在状态管理中不再使用
    ccbarDebugger('StateManager.updateSessionId被调用，但sessionId在状态功能中未使用');
  }

  /**
   * 设置座席就绪
   * @returns 设置结果
   */
  public async setAgentReady(): Promise<CCBarResponse> {
    return await this.setState(AgentStateType.IDLE);
  }

  /**
   * 设置座席未就绪
   * @param busyType 忙碌类型
   * @returns 设置结果
   */
  public async setAgentNotReady(busyType?: string): Promise<CCBarResponse> {
    return await this.setState(AgentStateType.BUSY, busyType);
  }

  /**
   * 设置话后整理状态
   * @returns 设置结果
   */
  public async setWorkNotReady(): Promise<CCBarResponse> {
    return await this.setState(AgentStateType.WORKNOTREADY);
  }

  /**
   * 完成话后整理
   * @returns 设置结果
   */
  public async setWorkReady(): Promise<CCBarResponse> {
    return await this.setState(AgentStateType.IDLE);
  }

  /**
   * 设置工作模式
   * @param mode 工作模式
   * @returns 操作结果
   */
  public async setWorkMode(mode: WorkModeType): Promise<CCBarResponse> {
    // 根据定义的枚举值，WorkModeType支持inbound、outbound、pdsbound和all
    if (![WorkModeType.INBOUND, WorkModeType.OUTBOUND, WorkModeType.PDS, WorkModeType.ALL].includes(mode)) {
      return {
        state: false,
        msg: '不支持的工作模式',
        data: {
          code: 'fail',
          content: '不支持的工作模式',
          result: null
        }
      };
    }

    try {
      // 构建工作模式变更请求参数
      const workModeParams = {
        cmdJson: JSON.stringify({
          cmd: 'setWorkMode',
          agentId: this.agentId,
          workMode: mode,
          event: 'setWorkMode',
          timestamp: new Date().getTime()
        })
      };

      // 发送工作模式变更请求
      const result = await this.requestService.post('/AgentEvent?action=event', workModeParams);

      // 处理响应结果
      if (result.state && (result.data?.code === 'succ' || result.data?.resultCode === '000')) {
        return {
          state: true,
          msg: '工作模式设置成功',
          data: {
            code: 'succ',
            content: '工作模式设置成功',
            result: {
              workMode: mode
            }
          }
        };
      } else {
        return {
          state: false,
          msg: result.data?.content || '工作模式设置失败',
          data: {
            code: 'fail',
            content: result.data?.content || '工作模式设置失败',
            result: null
          }
        };
      }
    } catch (error: any) {
      ccbarDebugger('工作模式设置异常', error);
      return {
        state: false,
        msg: error.message || '工作模式设置异常',
        data: {
          code: 'error',
          content: error.message || '工作模式设置异常',
          result: null
        }
      };
    }
  }

  /**
   * 获取功能状态
   * @param func 功能名称
   * @returns 功能是否可用
   */
  public getFuncMask(func: string): boolean {
    // 实际对接中应从服务端获取功能权限
    return true;
  }

  /**
   * 获取座席状态
   * @returns 座席状态
   */
  public getState(): string {
    return this.currentState;
  }

  /**
   * 设置座席状态
   * @param state 目标状态
   * @param busyType 忙碌类型（可选）
   * @returns 设置结果
   */
  public async setState(state: AgentStateType, busyType?: string): Promise<CCBarResponse> {
   
    try {
      // 构建状态变更请求参数
      let command = '';
      let event = '';

      // 根据目标状态确定命令
      if (state === AgentStateType.IDLE) {
        command = 'cmdReady';
        event = 'agentReady';
      } else if (state === AgentStateType.BUSY) {
        command = 'cmdNotReady';
        event = 'agentNotReady';
      } else if (state === AgentStateType.WORKNOTREADY) {
        command = 'cmdWorkNotReady';
        event = 'workNotReady';
      } else {
        return {
          state: false,
          msg: '不支持的状态变更类型',
          data: {
            code: 'fail',
            content: '不支持的状态变更类型',
            result: null
          }
        };
      }
      // 构建请求参数
      const stateParams: any = {
        data: {
          agentId: this.agentId || localStorage.getItem('agentId'),
          messageId: command,
          event: event,
          timestamp: new Date().getTime(),
          cuid: localStorage.getItem('cuid')
        }
      };

      // 如果有忙碌类型，添加到参数中
      if (busyType && state === AgentStateType.BUSY) {
        stateParams.cmdJson = JSON.stringify({
          ...JSON.parse(stateParams.cmdJson),
          busyType: busyType
        });
      }
      // 发送状态变更请求
      const result = await this.requestService.post('/AgentEvent?action=event', stateParams, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8'
        }
      });
      // 处理响应结果
      if (result.state) {
        // 更新当前状态
        this.currentState = state;

        // 触发状态变更事件
        // this.eventManager.emit('agent:stateChanged', {
        //   agentId: this.agentId,
        //   state: this.currentState,
        //   timestamp: new Date().getTime()
        // });

        return {
          state: true,
          msg: '状态变更成功',
          data: {
            code: 'succ',
            content: '状态变更成功',
            result: {
              state: this.currentState
            }
          }
        };
      } else {
        return {
          state: false,
          msg: result.data?.content || '状态变更失败',
          data: {
            code: 'fail',
            content: result.data?.content || '状态变更失败',
            result: null
          }
        };
      }
    } catch (error: any) {
      ccbarDebugger('状态变更异常', error);
      return {
        state: false,
        msg: error.message || '状态变更异常',
        data: {
          code: 'error',
          content: error.message || '状态变更异常',
          result: null
        }
      };
    }
  }

  /**
   * 获取当前状态
   * @returns 当前状态
   */
  public getCurrentState(): AgentStateType {
    return this.currentState;
  }

  /**
   * 更新当前状态（通常由事件触发）
   * @param state 新状态
   */
  public updateCurrentState(state: AgentStateType): void {
    const oldState = this.currentState;
    this.currentState = state;

    // 只有状态变化时才触发事件
    if (oldState !== state) {
      this.eventManager.emit('agent:stateChanged', {
        agentId: this.agentId,
        state: this.currentState,
        timestamp: new Date().getTime()
      });
    }
  }
} 