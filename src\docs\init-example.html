<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CCBar初始化示例</title>
  <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
  <!-- 引入打包后的CCBar资源 -->
  <script src="../ccbar.js"></script>
</head>
<body>
  <div id="app">
    <h1>CCBar初始化示例</h1>
    
    <div class="init-section">
      <h2>初始化方式</h2>
      <button onclick="initCCBar()">初始化CCBar</button>
      <div id="init-status"></div>
    </div>

    <div class="login-section">
      <h2>登录</h2>
      <div>
        <label for="username">用户名：</label>
        <input type="text" id="username" value="test_user">
      </div>
      <div>
        <label for="password">密码：</label>
        <input type="password" id="password" value="password">
      </div>
      <div>
        <label for="phone">电话：</label>
        <input type="text" id="phone" value="13800138000">
      </div>
      <div>
        <button onclick="loginWithService()">通过服务实例登录</button>
        <button onclick="loginDirect()">直接调用登录</button>
      </div>
      <div id="login-status"></div>
    </div>
  </div>

  <script>
    let ccbarService = null;

    // 初始化CCBar实例
    function initCCBar() {
      try {
        const config = {
          debug: true,
          baseURL: '/api/yc-ccbar-v1',
          wsURL: '/ws'
        };

        // 使用全局CCBar对象的getInstance方法
        if (window.CCBar && typeof window.CCBar.getInstance === 'function') {
          ccbarService = window.CCBar.getInstance(config);
          document.getElementById('init-status').textContent = '初始化成功！服务实例已创建。';
          console.log('CCBar实例创建成功:', ccbarService);
        } else {
          throw new Error('CCBar.getInstance方法未找到，请确保正确引入了CCBar库');
        }
      } catch (error) {
        document.getElementById('init-status').textContent = '初始化失败：' + error.message;
        console.error('初始化失败:', error);
      }
    }

    // 通过服务实例登录方法（原方法）
    function loginWithService() {
      if (!ccbarService) {
        document.getElementById('login-status').textContent = '请先初始化CCBar！';
        return;
      }

      const username = document.getElementById('username').value;
      const password = document.getElementById('password').value;
      const phone = document.getElementById('phone').value;

      try {
        // 使用服务实例的login方法
        ccbarService.login({
          loginAcct: username,
          loginPwd: password,
          phone: phone
        }).then(response => {
          document.getElementById('login-status').textContent = `服务实例登录成功！响应: ${JSON.stringify(response)}`;
          console.log('登录成功:', response);
        }).catch(error => {
          document.getElementById('login-status').textContent = `服务实例登录失败：${error.message}`;
          console.error('登录失败:', error);
        });
      } catch (error) {
        document.getElementById('login-status').textContent = '服务实例登录调用失败：' + error.message;
        console.error('登录调用失败:', error);
      }
    }
    
    // 直接使用全局CCBar.login方法
    function loginDirect() {
      const username = document.getElementById('username').value;
      const password = document.getElementById('password').value;
      const phone = document.getElementById('phone').value;

      try {
        // 在调用login前确保已初始化
        if (!window.CCBar.service) {
          console.log('检测到CCBar服务未初始化，先执行初始化...');
          const config = {
            debug: true,
            baseURL: '/api/yc-ccbar-v1',
            wsURL: '/ws'
          };
          window.CCBar.getInstance(config);
        }
        
        // 直接使用window.CCBar.login方法
        if (window.CCBar && typeof window.CCBar.login === 'function') {
          window.CCBar.login({
            loginAcct: username,
            loginPwd: password,
            phone: phone
          }).then(response => {
            document.getElementById('login-status').textContent = `直接登录成功！响应: ${JSON.stringify(response)}`;
            console.log('直接登录成功:', response);
          }).catch(error => {
            document.getElementById('login-status').textContent = `直接登录失败：${error.message}`;
            console.error('直接登录失败:', error);
          });
        } else {
          throw new Error('window.CCBar.login方法未找到，请确保正确初始化了CCBar');
        }
      } catch (error) {
        document.getElementById('login-status').textContent = '直接登录调用失败：' + error.message;
        console.error('直接登录调用失败:', error);
      }
    }

    // 检测全局变量
    function checkGlobalVariable() {
      if (window.CCBar) {
        console.log('全局CCBar对象存在');
        
        // 检测核心API是否存在
        let apis = ['getInstance', 'login', 'logout', 'agentReady', 'agentNotReady', 'makeCall', 'answerCall'];
        
        apis.forEach(api => {
          if (typeof window.CCBar[api] === 'function') {
            console.log(`CCBar.${api}方法可用`);
          } else {
            console.error(`CCBar.${api}方法不存在`);
          }
        });
      } else {
        console.error('全局CCBar对象不存在');
      }
    }

    // 页面加载完成后进行检查
    window.onload = function() {
      checkGlobalVariable();
    };
  </script>

  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .init-section, .login-section {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    input, button {
      margin: 10px 5px 10px 0;
      padding: 8px;
    }
    #init-status, #login-status {
      margin-top: 10px;
      padding: 10px;
      background-color: #f8f8f8;
      border-radius: 4px;
      min-height: 20px;
    }
  </style>
</body>
</html> 