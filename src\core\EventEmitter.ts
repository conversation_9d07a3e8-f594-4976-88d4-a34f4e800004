/**
 * 自定义事件系统，提供事件监听和触发功能
 */
export class EventEmitter {
  private listeners: Record<string, Function[]> = {};
  private static instance: EventEmitter;
  private isEmitting: Record<string, boolean> = {};

  private constructor() {}

  /**
   * 获取事件发射器单例
   */
  public static getInstance(): EventEmitter {
    if (!EventEmitter.instance) {
      EventEmitter.instance = new EventEmitter();
    }
    return EventEmitter.instance;
  }

  /**
   * 添加事件监听
   * @param type 事件类型
   * @param fn 回调函数
   */
  public on(type: string, fn: Function): EventEmitter {
    if (typeof type === 'string' && typeof fn === 'function') {
      if (!this.listeners[type]) {
        this.listeners[type] = [];
      }
      this.listeners[type].push(fn);
    }
    return this;
  }

  /**
   * 添加多个事件监听
   * @param obj 事件映射对象
   */
  public addEvents(obj: Record<string, Function>): EventEmitter {
    for (const type in obj) {
      if (obj.hasOwnProperty(type) && typeof obj[type] === 'function') {
        this.on(type, obj[type]);
      }
    }
    return this;
  }

  /**
   * 触发事件
   * @param type 事件类型
   * @param args 事件参数
   */
  public emit(type: string, ...args: any[]): EventEmitter {
    // 检查是否已在发送同一类型事件，避免死循环
    if (this.isEmitting[type]) {
      console.warn(`检测到可能的事件循环: ${type}`);
      return this;
    }

    this.isEmitting[type] = true;
    console.log('触发事件', type, args);
    try {
      if (type && this.listeners[type]) {
        const events = {
          type,
          target: this
        };
        
        const handlers = [...this.listeners[type]];
        for (let i = 0; i < handlers.length; i++) {
          try {
            handlers[i].apply(this, [events, ...args]);
          } catch (e) {
            console.error(`Error in event handler for ${type}:`, e);
          }
        }
      }
    } finally {
      this.isEmitting[type] = false;
    }
    
    return this;
  }

  /**
   * 删除事件监听
   * @param type 事件类型
   * @param fn 回调函数
   */
  public off(type: string, fn?: Function): EventEmitter {
    const listeners = this.listeners[type];
    
    if (Array.isArray(listeners)) {
      if (typeof fn === 'function') {
        // 移除特定监听器
        const index = listeners.indexOf(fn);
        if (index !== -1) {
          listeners.splice(index, 1);
        }
      } else {
        // 移除所有该类型的监听器
        delete this.listeners[type];
      }
    }
    
    return this;
  }

  /**
   * 移除多个事件监听
   * @param types 事件类型数组或事件映射对象
   */
  public removeEvents(types: string[] | Record<string, Function>): EventEmitter {
    if (Array.isArray(types)) {
      // 移除多个事件类型
      for (let i = 0; i < types.length; i++) {
        this.off(types[i]);
      }
    } else if (typeof types === 'object') {
      // 移除特定事件和处理器
      for (const type in types) {
        if (types.hasOwnProperty(type)) {
          this.off(type, types[type]);
        }
      }
    }
    
    return this;
  }

  /**
   * 一次性事件监听
   * @param type 事件类型
   * @param fn 回调函数
   */
  public once(type: string, fn: Function): EventEmitter {
    const wrapper = (...args: any[]) => {
      fn.apply(this, args);
      this.off(type, wrapper);
    };
    
    return this.on(type, wrapper);
  }
} 