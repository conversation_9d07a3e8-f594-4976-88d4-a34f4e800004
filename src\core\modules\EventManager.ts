/**
 * 事件管理模块
 * 负责事件的注册、触发和移除
 */

import { IEventManager } from '../interfaces';
import { EventEmitter } from '../EventEmitter';
import { ccbarDebugger } from '../../utils';

/**
 * 事件管理器实现类
 */
export class EventManager implements IEventManager {
  private eventEmitter: EventEmitter;

  /**
   * 构造函数
   * @param eventEmitter 事件发射器
   */
  constructor(eventEmitter: EventEmitter) {
    this.eventEmitter = eventEmitter;
  }

  /**
   * 注册事件监听器
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  public on(eventName: string, callback: Function): void {
    ccbarDebugger('EventManager.on', eventName);
    this.eventEmitter.on(eventName, callback);
  }

  /**
   * 移除事件监听器
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  public off(eventName: string, callback: Function): void {
    ccbarDebugger('EventManager.off', eventName);
    this.eventEmitter.off(eventName, callback);
  }

  /**
   * 注册一次性事件监听器
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  public once(eventName: string, callback: Function): void {
    ccbarDebugger('EventManager.once', eventName);
    this.eventEmitter.once(eventName, callback);
  }

  /**
   * 触发事件
   * @param eventName 事件名称
   * @param data 事件数据
   */
  public emit(eventName: string, data?: any): void {
    ccbarDebugger('EventManager.emit', eventName, data);
    this.eventEmitter.emit(eventName, data);
  }
} 