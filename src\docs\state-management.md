# CCBar 统一状态管理方案

## 简介

为了确保话务条（CCBar）的所有状态管理集中且一致，我们实现了全局状态管理系统。该系统负责管理所有与座席状态、登录状态、通话状态相关的信息，确保在整个应用中的状态一致性。

## 设计目标

1. **单一数据源**：整个应用只有一个状态管理中心，避免多个模块各自维护状态导致的不一致
2. **高容错性**：能够自动处理异常情况，确保系统状态稳定
3. **状态同步**：所有组件和服务能够获取到最新的状态信息
4. **历史记录**：保留状态变更历史，便于调试和问题追踪
5. **简化接口**：为开发者提供简洁明了的API，降低使用难度

## 核心组件

### 1. GlobalStateManager

全局状态管理器，实现为单例模式，负责管理所有状态：

- 座席状态（空闲、忙碌、通话中等）
- 登录状态
- 功能掩码（哪些功能可用）
- 通话信息
- 错误处理

### 2. 全局状态工具模块 (core/state.ts)

提供便捷访问全局状态的工具函数：

```typescript
import { globalState } from '../core/state';

// 获取当前状态
const currentState = globalState.getState();

// 检查功能是否可用
if (globalState.isFuncEnabled(FuncMaskType.MAKECALL)) {
  // 允许拨打电话
}
```

### 3. Vue组件状态钩子 (useAgentState.ts)

为Vue组件提供响应式的状态数据和方法：

```typescript
import { useAgentState } from '../hooks/useAgentState';

export default {
  setup() {
    const { agentState, isLoggedIn, isFuncEnabled } = useAgentState();
    
    return {
      // 在模板中可以直接使用
      agentState,
      isLoggedIn,
      isFuncEnabled
    };
  }
}
```

## 状态流转

1. **状态初始化**：应用启动时，`GlobalStateManager`被初始化为默认状态（未登录）
2. **登录流程**：
   - 用户登录成功后，`ConnectionManager`发出`agent:loggedIn`事件
   - `GlobalStateManager`捕获事件并更新登录状态与座席状态
   - 通过事件系统通知所有订阅者状态已更新
3. **状态变更**：
   - 所有状态变更操作都通过`GlobalStateManager`执行
   - 状态变更自动更新功能掩码（根据当前状态决定哪些功能可用）
   - 状态历史被记录，便于调试

## 错误处理

全局状态管理器内置了错误处理机制：

1. 捕获全局未处理的错误和Promise拒绝
2. 维护最后的错误信息，便于调试
3. 出现错误时自动执行状态一致性检查，尝试恢复到一致状态

## 状态一致性

如果检测到状态不一致（例如，显示为已登录但状态为未登录），系统会自动修复以确保一致性。

## 使用方式

### 在服务中使用

```typescript
import { globalState } from '../core/state';

// 在登录成功后
globalState.updateLoginState(true, sessionId, agentId);

// 在状态变更时
globalState.setState(AgentStateType.READY, '空闲', '已设置为就绪状态');
```

### 在Vue组件中使用

```typescript
<template>
  <div>
    <p>当前状态: {{ agentState.stateDesc }}</p>
    <button 
      v-if="isFuncEnabled(FuncMaskType.AGENTREADY)" 
      @click="setReady">
      置闲
    </button>
  </div>
</template>

<script>
import { useAgentState } from '../hooks/useAgentState';
import { FuncMaskType } from '../types';
import { CCBarService } from '../core/CCBarService';

export default {
  setup() {
    const { agentState, isFuncEnabled } = useAgentState();
    const ccbar = CCBarService.getInstance({}); // 获取CCBar服务实例
    
    const setReady = () => {
      ccbar.agentReady(); // 通过服务执行操作，会自动更新状态
    };
    
    return {
      agentState,
      isFuncEnabled,
      FuncMaskType,
      setReady
    };
  }
}
</script>
```

## 优势

1. **简化开发**：组件不需要直接处理复杂的状态逻辑
2. **减少错误**：所有状态管理集中在一处，降低状态不一致的风险
3. **提高可维护性**：状态变更有清晰的历史记录和错误追踪
4. **增强稳定性**：内置错误处理和状态恢复机制
5. **响应式支持**：通过Vue hook，轻松在组件中使用响应式状态

## 全局状态管理

CCBar系统使用GlobalStateManager作为全局状态管理器，遵循单例模式确保系统中只有一个状态管理实例。

### 核心概念

1. **座席状态**: 通过`AgentStateType`枚举定义，包括LOGOFF（未登录）、IDLE（空闲）、BUSY（繁忙）等状态。
2. **登录状态**: 系统通过座席状态来判断登录状态，当座席状态为`AgentStateType.LOGOFF`时表示未登录，其他状态表示已登录。
3. **功能掩码**: 根据不同状态提供不同的功能权限，例如在IDLE状态下可以拨打电话，但在TALK状态下不能。
4. **状态历史**: 记录状态变更历史，便于追踪问题。

### 状态变更流程

当发生状态变更时，以下事件会被触发：

- `state:changed`: 状态发生变化
- `agent:stateChanged`: 座席状态变化（兼容性事件）
- `agentStateSync`: 状态同步事件（兼容性事件）

### 登录状态判断

在之前的实现中，我们使用单独的`loggedIn`标志来表示是否登录。现在我们进行了重构：

- 不再使用单独的`loggedIn`标志。
- 根据座席状态来判断登录状态：当座席状态为`AgentStateType.LOGOFF`时表示未登录，其他状态表示已登录。
- 通过`isLoggedIn()`方法判断登录状态，该方法返回`this.agentState.state !== AgentStateType.LOGOFF`。

这样的设计使得状态管理更加简洁，减少了状态不一致的可能性。当座席登出时，系统将其状态设置为`AgentStateType.LOGOFF`，同时更新状态描述为"未登录"。

### 使用方法

在组件中使用全局状态管理：

```typescript
import { useAgentState } from './hooks/useAgentState';

// 在组件中使用
const { agentState, isLoggedIn, isFuncEnabled } = useAgentState();

// 判断是否登录
if (isLoggedIn.value) {
  // 登录后的操作
}

// 判断某功能是否可用
if (isFuncEnabled(FuncMaskType.MAKECALL)) {
  // 可以拨打电话
}
```

### 事件流程

- 用户登录成功后，`ConnectionManager`发出`agent:loggedIn`事件
- 状态管理器捕获此事件，更新登录状态和座席状态
- 座席状态变更时，状态管理器发出`state:changed`等事件
- 组件使用`useAgentState`钩子获取响应式状态，并根据状态变更更新UI 