/**
 * 座席状态钩子
 * 用于在Vue组件中订阅和使用全局座席状态
 */

import { ref, onMounted, onUnmounted, Ref } from 'vue';
import { AgentState, FuncMaskType } from '../types';
import { globalState } from '../core/state';
import { EventEmitter } from '../core/EventEmitter';

/**
 * 座席状态钩子
 * @returns 座席状态相关的响应式数据和方法
 */
export function useAgentState() {
  // 获取事件发射器实例
  const eventEmitter = EventEmitter.getInstance();
  
  // 响应式状态
  const agentState: Ref<AgentState> = ref(globalState.getState());
  const isLoggedIn: Ref<boolean> = ref(globalState.isLoggedIn());
  const agentId: Ref<string> = ref(globalState.getAgentId());
  const stateHistory: Ref<Array<{timestamp: number, state: string, description: string}>> = ref(globalState.getStateHistory());
  const lastError: Ref<string | null> = ref(globalState.getLastError());
  
  // 状态变更处理函数
  const handleStateChanged = (event: any) => {
    agentState.value = event.currentState;
  };
  
  // 状态同步处理函数
  const handleAgentStateSync = (state: AgentState) => {
    agentState.value = state;
  };
  
  // 登录状态变更处理函数
  const handleLoginChanged = () => {
    isLoggedIn.value = globalState.isLoggedIn();
    agentId.value = globalState.getAgentId();
  };
  
  // 错误处理函数
  const handleError = (event: { error: string }) => {
    lastError.value = event.error;
  };
  
  // 组件挂载时注册事件监听
  onMounted(() => {
    globalState.onStateChanged(handleStateChanged);
    globalState.onAgentStateSync(handleAgentStateSync);
    eventEmitter.on('agent:loggedIn', handleLoginChanged);
    globalState.onError(handleError);
  });
  
  // 组件卸载时移除事件监听
  onUnmounted(() => {
    globalState.offStateChanged(handleStateChanged);
    globalState.offAgentStateSync(handleAgentStateSync);
    eventEmitter.off('agent:loggedIn', handleLoginChanged);
    globalState.offError(handleError);
  });
  
  /**
   * 检查功能是否可用
   * @param func 功能名称
   * @returns 功能是否可用
   */
  const isFuncEnabled = (func: FuncMaskType): boolean => {
    return globalState.isFuncEnabled(func);
  };
  
  /**
   * 刷新状态历史
   */
  const refreshStateHistory = (): void => {
    stateHistory.value = globalState.getStateHistory();
  };
  
  /**
   * 获取状态描述
   * @param state 状态码
   * @returns 状态描述
   */
  const getStateDesc = (state: string): string => {
    // 从当前状态中获取状态描述
    return agentState.value.state === state ? 
      agentState.value.stateDesc : 
      state;
  };
  
  // 返回响应式数据和方法
  return {
    // 响应式状态
    agentState,
    isLoggedIn,
    agentId,
    stateHistory,
    lastError,
    
    // 实用方法
    isFuncEnabled,
    refreshStateHistory,
    getStateDesc
  };
}

export default useAgentState; 