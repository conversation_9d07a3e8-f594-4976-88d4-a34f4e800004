/**
 * 转接管理模块
 * 负责管理呼叫转接
 */

import { ITransferManager, IEventManager } from '../interfaces';
import { TransferCallParams, CCBarResponse } from '../../types';
import { RequestService } from '../../utils/request';
import { ccbarDebugger } from '../../utils';

/**
 * 转接管理器实现类
 */
export class TransferManager implements ITransferManager {
  private requestService: RequestService;
  private eventManager: IEventManager;
  private agentId: string;
  private currentCallId: string | null = null;
  private isTransferring: boolean = false;

  /**
   * 构造函数
   * @param requestService HTTP请求服务
   * @param eventManager 事件管理器
   * @param agentId 座席ID
   */
  constructor(
    requestService: RequestService,
    eventManager: IEventManager,
    agentId: string
  ) {
    this.requestService = requestService;
    this.eventManager = eventManager;
    this.agentId = agentId;
    
    // 监听来电事件，更新当前呼叫ID
    this.eventManager.on('call:incoming', (data: any) => {
      if (data && data.callId) {
        this.currentCallId = data.callId;
      }
    });
    
    // 监听通话结束事件，清空当前呼叫ID和转接状态
    this.eventManager.on('call:ended', () => {
      this.currentCallId = null;
      this.isTransferring = false;
    });
  }

  /**
   * 更新会话ID
   * @param sessionId 新的会话ID
   * @deprecated sessionId在转接管理中不再使用，但为保持API兼容性而保留
   */
  public updateSessionId(sessionId: string): void {
    ccbarDebugger('TransferManager.updateSessionId被调用，但sessionId在转接功能中未使用');
    // sessionId在转接功能中不再使用
  }

  /**
   * 更新当前呼叫ID
   * @param callId 呼叫ID
   */
  public updateCurrentCallId(callId: string): void {
    this.currentCallId = callId;
  }

  /**
   * 发起呼叫转接
   * @param params 转接参数
   * @returns 转接结果
   */
  public async transferCall(params: TransferCallParams): Promise<CCBarResponse> {
    if (!this.currentCallId) {
      return {
        state: false,
        msg: '没有可转接的呼叫',
        data: {
          code: 'fail',
          content: '没有可转接的呼叫',
          result: null
        }
      };
    }
    
    if (this.isTransferring) {
      return {
        state: false,
        msg: '已有转接正在进行中',
        data: {
          code: 'fail',
          content: '已有转接正在进行中',
          result: null
        }
      };
    }

    try {
      // 构建转接请求参数
      const transferParams: any = {
        data: {
          messageId: params.isDirectTransfer ? 'cmdBlindTransfer' : 'cmdSupervisedTransfer',
          agentId: this.agentId || localStorage.getItem('agentId'),
          callId: this.currentCallId,
          cuid: localStorage.getItem('cuid') || '',
          timestamp: new Date().getTime()
        }
      };
      
      // 根据转接目标类型设置参数
      if (params.targetType === 'agent') {
        transferParams.cmdJson = JSON.stringify({
          ...JSON.parse(transferParams.cmdJson),
          targetAgentId: params.agentId,
          agentDN: params.agentPhone
        });
      } else if (params.targetType === 'phone') {
        transferParams.cmdJson = JSON.stringify({
          ...JSON.parse(transferParams.cmdJson),
          phoneNumber: params.phoneNumber,
          callerId: params.displayNumber || ''
        });
      }
      
      // 发送转接请求
      const result = await this.requestService.post('/AgentEvent?action=event', transferParams);
      
      // 处理响应结果
      if (result.state && (result.data?.code === 'succ' || result.data?.resultCode === '000')) {
        this.isTransferring = true;
        
        // 触发转接中事件
        this.eventManager.emit('call:transferring', {
          callId: this.currentCallId,
          targetType: params.targetType,
          targetId: params.targetType === 'agent' ? params.agentId : params.phoneNumber,
          isDirectTransfer: params.isDirectTransfer,
          timestamp: new Date().getTime()
        });
        
        return {
          state: true,
          msg: '转接请求已发送',
          data: {
            code: 'succ',
            content: '转接请求已发送',
            result: result.data.result
          }
        };
      } else {
        return {
          state: false,
          msg: result.data?.content || '转接失败',
          data: {
            code: 'fail',
            content: result.data?.content || '转接失败',
            result: null
          }
        };
      }
    } catch (error: any) {
      ccbarDebugger('转接异常', error);
      return {
        state: false,
        msg: error.message || '转接异常',
        data: {
          code: 'error',
          content: error.message || '转接异常',
          result: null
        }
      };
    }
  }

  /**
   * 完成转接
   * @returns 完成转接结果
   */
  public async completeTransfer(): Promise<CCBarResponse> {
    if (!this.isTransferring) {
      return {
        state: false,
        msg: '没有正在进行的转接',
        data: {
          code: 'fail',
          content: '没有正在进行的转接',
          result: null
        }
      };
    }

    try {
      // 构建完成转接请求参数
      const completeParams = {
        cmdJson: JSON.stringify({
          cmd: 'completeTransfer',
          agentId: this.agentId,
          callId: this.currentCallId,
          timestamp: new Date().getTime()
        })
      };
      
      // 发送完成转接请求
      const result = await this.requestService.post('/AgentEvent?action=event', completeParams);
      
      // 处理响应结果
      if (result.state && (result.data?.code === 'succ' || result.data?.resultCode === '000')) {
        this.isTransferring = false;
        this.currentCallId = null;
        
        // 触发转接完成事件
        this.eventManager.emit('call:transferred', {
          timestamp: new Date().getTime()
        });
        
        return {
          state: true,
          msg: '转接已完成',
          data: {
            code: 'succ',
            content: '转接已完成',
            result: result.data.result
          }
        };
      } else {
        return {
          state: false,
          msg: result.data?.content || '完成转接失败',
          data: {
            code: 'fail',
            content: result.data?.content || '完成转接失败',
            result: null
          }
        };
      }
    } catch (error: any) {
      ccbarDebugger('完成转接异常', error);
      return {
        state: false,
        msg: error.message || '完成转接异常',
        data: {
          code: 'error',
          content: error.message || '完成转接异常',
          result: null
        }
      };
    }
  }

  /**
   * 取消转接
   * @returns 取消转接结果
   */
  public async cancelTransfer(): Promise<CCBarResponse> {
    if (!this.isTransferring) {
      return {
        state: false,
        msg: '没有正在进行的转接',
        data: {
          code: 'fail',
          content: '没有正在进行的转接',
          result: null
        }
      };
    }
    
    try {
      // 构建取消转接请求参数
      const cancelParams = {
        cmdJson: JSON.stringify({
          cmd: 'cancelTransfer',
          agentId: this.agentId,
          callId: this.currentCallId,
          timestamp: new Date().getTime()
        })
      };
      
      // 发送取消转接请求
      const result = await this.requestService.post('/AgentEvent?action=event', cancelParams);
      
      // 处理响应结果
      if (result.state && (result.data?.code === 'succ' || result.data?.resultCode === '000')) {
        this.isTransferring = false;
        
        // 触发取消转接事件
        this.eventManager.emit('call:transfercancelled', {
          callId: this.currentCallId,
          timestamp: new Date().getTime()
        });
        
        return {
          state: true,
          msg: '转接已取消',
          data: {
            code: 'succ',
            content: '转接已取消',
            result: result.data.result
          }
        };
      } else {
        return {
          state: false,
          msg: result.data?.content || '取消转接失败',
          data: {
            code: 'fail',
            content: result.data?.content || '取消转接失败',
            result: null
          }
        };
      }
    } catch (error: any) {
      ccbarDebugger('取消转接异常', error);
      return {
        state: false,
        msg: error.message || '取消转接异常',
        data: {
          code: 'error',
          content: error.message || '取消转接异常',
          result: null
        }
      };
    }
  }
  
  /**
   * 获取转接状态
   * @returns 是否正在转接中
   */
  public isInTransfer(): boolean {
    return this.isTransferring;
  }
} 