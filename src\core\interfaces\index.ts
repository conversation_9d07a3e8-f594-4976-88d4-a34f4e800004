/**
 * CCBar服务接口定义
 * 所有功能模块接口集中导出
 */

import { 
  LoginParams, 
  CCBarResponse, 
  CCBarConfig, 
  WorkModeType,
  TransferCallParams 
} from '../../types';

// 导出所有模块接口
export * from './ICallManager';
export * from './IConnectionManager';
export * from './IStateManager';
export * from './ITransferManager';
export * from './ISupervisorManager';

/**
 * 事件管理接口
 * 负责事件的注册、触发和移除
 */
export interface IEventManager {
  /**
   * 注册事件监听器
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  on(eventName: string, callback: Function): void;
  
  /**
   * 移除事件监听器
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  off(eventName: string, callback: Function): void;
  
  /**
   * 注册一次性事件监听器
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  once(eventName: string, callback: Function): void;
  
  /**
   * 触发事件
   * @param eventName 事件名称
   * @param data 事件数据
   */
  emit(eventName: string, data?: any): void;
}

/**
 * 连接管理接口
 * 负责座席登录、登出和会话维护
 */
export interface IConnectionManager {
  initCCbar(): CCBarResponse<any> | PromiseLike<CCBarResponse<any>>;
  /**
   * 座席登录
   * @param params 登录参数
   * @returns 登录结果
   */
  login(params: LoginParams): Promise<CCBarResponse>;
  
  /**
   * 座席登出
   * @returns 登出结果
   */
  logout(): Promise<CCBarResponse>;
  
  /**
   * 获取连接状态
   * @returns 是否已连接
   */
  isConnected(): boolean;
  
  /**
   * 获取座席信息
   * @returns 座席信息
   */
  getAgentInfo(): any;
}

/**
 * 状态管理接口
 * 负责座席状态的管理和查询
 */
export interface IStateManager {
  /**
   * 设置座席就绪
   * @returns 操作结果
   */
  setAgentReady(): Promise<CCBarResponse>;
  
  /**
   * 设置座席未就绪
   * @param busyType 忙碌类型
   * @returns 操作结果
   */
  setAgentNotReady(busyType?: string): Promise<CCBarResponse>;
  
  /**
   * 设置话后整理状态
   * @returns 操作结果
   */
  setWorkNotReady(): Promise<CCBarResponse>;
  
  /**
   * 设置完成话后整理
   * @returns 操作结果
   */
  setWorkReady(): Promise<CCBarResponse>;
  
  /**
   * 设置工作模式
   * @param mode 工作模式
   * @returns 操作结果
   */
  setWorkMode(mode: WorkModeType): Promise<CCBarResponse>;
  
  /**
   * 获取功能状态
   * @param func 功能名称
   * @returns 功能是否可用
   */
  getFuncMask(func: string): boolean;
  
  /**
   * 获取座席状态
   * @returns 座席状态
   */
  getState(): string;
}

/**
 * 呼叫管理接口
 * 负责电话呼叫的管理
 */
export interface ICallManager {
  /**
   * 拨打电话
   * @param phoneNumber 电话号码
   * @param displayNumber 外显号码
   * @param userData 用户数据
   * @param callType 呼叫类型
   * @returns 操作结果
   */
  makeCall(
    phoneNumber: string, 
    displayNumber?: string, 
    userData?: any, 
    callType?: number
  ): Promise<CCBarResponse>;
  
  /**
   * 应答来电
   * @returns 操作结果
   */
  answerCall(): Promise<CCBarResponse>;
  
  /**
   * 挂断电话
   * @returns 操作结果
   */
  clearCall(): Promise<CCBarResponse>;
  
  /**
   * 保持通话
   * @returns 操作结果
   */
  holdCall(): Promise<CCBarResponse>;
  
  /**
   * 恢复通话
   * @returns 操作结果
   */
  unholdCall(): Promise<CCBarResponse>;
  
  /**
   * 获取当前呼叫信息
   * @returns 呼叫信息对象
   */
  getCurrentCall(): any | null;
}

/**
 * 转接管理接口
 * 负责呼叫转接的管理
 */
export interface ITransferManager {
  /**
   * 转接通话
   * @param params 转接参数
   * @returns 操作结果
   */
  transferCall(params: TransferCallParams): Promise<CCBarResponse>;
  
  /**
   * 完成转接
   * @returns 操作结果
   */
  completeTransfer(): Promise<CCBarResponse>;
  
  /**
   * 取消转接
   * @returns 操作结果
   */
  cancelTransfer(): Promise<CCBarResponse>;
} 