import { CCBarResponse } from '../../types';

/**
 * 呼叫管理器接口
 * 负责管理呼叫操作，如外呼、应答、挂断、保持等
 */
export interface ICallManager {
  /**
   * 更新会话ID
   * @param sessionId 新的会话ID
   */
  updateSessionId(sessionId: string): void;
  
  /**
   * 发起呼叫
   * @param phoneNumber 呼叫的电话号码
   * @param displayNumber 外显号码
   * @returns 呼叫结果
   */
  makeCall(phoneNumber: string, displayNumber?: string): Promise<CCBarResponse>;
  
  /**
   * 应答呼叫
   * @param callId 呼叫ID，不传则使用当前呼叫
   * @returns 应答结果
   */
  answerCall(callId?: string): Promise<CCBarResponse>;
  
  /**
   * 挂断呼叫
   * @param callId 呼叫ID，不传则使用当前呼叫
   * @returns 挂断结果
   */
  hangupCall(callId?: string): Promise<CCBarResponse>;
  
  /**
   * 保持呼叫
   * @param callId 呼叫ID，不传则使用当前呼叫
   * @returns 保持结果
   */
  holdCall(callId?: string): Promise<CCBarResponse>;
  
  /**
   * 取回呼叫
   * @param callId 呼叫ID，不传则使用当前呼叫
   * @returns 取回结果
   */
  retrieveCall(callId?: string): Promise<CCBarResponse>;
  
  /**
   * 获取当前呼叫ID
   * @returns 当前呼叫ID，如果没有则返回null
   */
  getCurrentCallId(): string | null;
  
  /**
   * 设置当前呼叫ID
   * @param callId 呼叫ID
   */
  setCurrentCallId(callId: string): void;
  
  /**
   * 清空当前呼叫ID
   */
  clearCurrentCallId(): void;
  
  /**
   * 麦克风静音
   * @param callId 呼叫ID，不传则使用当前呼叫
   * @returns 静音结果
   */
  muteCall(callId?: string): Promise<CCBarResponse>;
  
  /**
   * 取消麦克风静音
   * @param callId 呼叫ID，不传则使用当前呼叫
   * @returns 取消静音结果
   */
  unmuteCall(callId?: string): Promise<CCBarResponse>;
} 