import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { ApiResponse, HttpConfig, RequestOptions } from '../types/api';

/**
 * HTTP请求服务类
 * 负责处理所有HTTP请求，包括拦截器配置、请求发送、响应处理等
 */
export class HttpService {
  private axiosInstance: AxiosInstance;
  private static instance: HttpService;
  private baseConfig: HttpConfig;

  private constructor(config: HttpConfig) {
    this.baseConfig = config;
    this.axiosInstance = axios.create({
      baseURL: config.baseURL,
      timeout: config.timeout || 10000,
      headers: config.headers || {
        'Content-Type': 'application/json'
      }
    });
    
    this.setupInterceptors();
  }

  /**
   * 获取HttpService单例
   * @param config HTTP配置
   */
  public static getInstance(config: HttpConfig): HttpService {
    if (!HttpService.instance) {
      HttpService.instance = new HttpService(config);
    } else if (config.baseURL !== HttpService.instance.baseConfig.baseURL) {
      // 如果baseURL变化，重新创建实例
      HttpService.instance = new HttpService(config);
    }
    return HttpService.instance;
  }

  /**
   * 设置请求和响应拦截器
   */
  private setupInterceptors(): void {
    // 请求拦截器
    this.axiosInstance.interceptors.request.use(
      (config) => {
        // 添加会话ID到请求头（如果存在）
        if (this.baseConfig.sessionId) {
          config.headers['Session-ID'] = this.baseConfig.sessionId;
        }
        
        // TODO: [真实接口对接] 添加认证信息
        // 例如：config.headers['Authorization'] = `Bearer ${token}`;
        
        // 触发请求前回调
        this.baseConfig.onRequest?.();
        
        return config;
      },
      (error: AxiosError) => {
        // 请求错误处理
        this.baseConfig.onError?.(error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.axiosInstance.interceptors.response.use(
      (response: AxiosResponse) => {
        // 触发请求完成回调
        this.baseConfig.onResponse?.();
        
        // TODO: [真实接口对接] 统一处理API响应格式
        // 例如：检查业务状态码、提取数据等
        
        return response;
      },
      (error: AxiosError) => {
        // 响应错误处理
        this.baseConfig.onResponse?.();
        this.baseConfig.onError?.(error);
        
        // 会话超时处理
        if (error.response?.status === 401) {
          // TODO: [真实接口对接] 处理会话超时
          console.error('会话已超时，请重新登录');
          this.baseConfig.onSessionTimeout?.();
        }
        
        return Promise.reject(error);
      }
    );
  }

  /**
   * 设置会话ID
   * @param sessionId 会话ID
   */
  public setSessionId(sessionId: string): void {
    this.baseConfig.sessionId = sessionId;
  }

  /**
   * 设置请求前回调
   * @param callback 回调函数
   */
  public setRequestCallback(callback: () => void): void {
    this.baseConfig.onRequest = callback;
  }

  /**
   * 设置响应回调
   * @param callback 回调函数
   */
  public setResponseCallback(callback: () => void): void {
    this.baseConfig.onResponse = callback;
  }

  /**
   * 设置错误回调
   * @param callback 回调函数
   */
  public setErrorCallback(callback: (error: any) => void): void {
    this.baseConfig.onError = callback;
  }

  /**
   * 设置会话超时回调
   * @param callback 回调函数
   */
  public setSessionTimeoutCallback(callback: () => void): void {
    this.baseConfig.onSessionTimeout = callback;
  }

  /**
   * 发送GET请求
   * @param url 请求URL
   * @param params 请求参数
   * @param options 请求选项
   */
  public async get<T = any>(
    url: string, 
    params?: Record<string, any>, 
    options?: RequestOptions
  ): Promise<ApiResponse<T>> {
    try {
      const response = await this.axiosInstance.get(url, {
        params,
        ...options
      });
      return this.handleResponse<T>(response);
    } catch (error) {
      return this.handleError<T>(error as AxiosError);
    }
  }

  /**
   * 发送POST请求
   * @param url 请求URL
   * @param data 请求数据
   * @param options 请求选项
   */
  public async post<T = any>(
    url: string, 
    data?: Record<string, any>, 
    options?: RequestOptions
  ): Promise<ApiResponse<T>> {
    try {
      const response = await this.axiosInstance.post(url, data, options);
      return this.handleResponse<T>(response);
    } catch (error) {
      return this.handleError<T>(error as AxiosError);
    }
  }

  /**
   * 发送PUT请求
   * @param url 请求URL
   * @param data 请求数据
   * @param options 请求选项
   */
  public async put<T = any>(
    url: string, 
    data?: Record<string, any>, 
    options?: RequestOptions
  ): Promise<ApiResponse<T>> {
    try {
      const response = await this.axiosInstance.put(url, data, options);
      return this.handleResponse<T>(response);
    } catch (error) {
      return this.handleError<T>(error as AxiosError);
    }
  }

  /**
   * 发送DELETE请求
   * @param url 请求URL
   * @param params 请求参数
   * @param options 请求选项
   */
  public async delete<T = any>(
    url: string, 
    params?: Record<string, any>, 
    options?: RequestOptions
  ): Promise<ApiResponse<T>> {
    try {
      const response = await this.axiosInstance.delete(url, {
        params,
        ...options
      });
      return this.handleResponse<T>(response);
    } catch (error) {
      return this.handleError<T>(error as AxiosError);
    }
  }

  /**
   * 处理响应
   * @param response Axios响应对象
   */
  private handleResponse<T>(response: AxiosResponse): ApiResponse<T> {
    // TODO: [真实接口对接] 根据实际API响应格式调整
    return {
      success: true,
      data: response.data?.data?.result || response.data,
      message: response.data?.data?.content || '请求成功',
      code: response.data?.data?.code || 'SUCCESS',
      originalResponse: response
    };
  }

  /**
   * 处理错误
   * @param error Axios错误对象
   */
  private handleError<T>(error: AxiosError): ApiResponse<T> {
    return {
      success: false,
      data: null,
      message: error.response?.data?.message || error.message || '请求失败',
      code: error.response?.data?.code || 'ERROR',
      originalError: error
    };
  }

  /**
   * 发送JSONP请求
   * @param url 请求URL
   * @param data 请求数据
   * @param callbackParam 回调参数名
   */
  public async jsonp<T = any>(
    url: string, 
    data?: Record<string, any>, 
    callbackParam: string = 'callback'
  ): Promise<ApiResponse<T>> {
    return new Promise((resolve) => {
      const script = document.createElement('script');
      const callbackName = `jsonp_${Date.now()}_${Math.ceil(Math.random() * 1000000)}`;
      
      // 构建完整URL
      const params = new URLSearchParams(data);
      params.append(callbackParam, callbackName);
      script.src = `${url}${url.includes('?') ? '&' : '?'}${params.toString()}`;
      
      // 触发请求前回调
      this.baseConfig.onRequest?.();
      
      // 定义回调函数
      (window as any)[callbackName] = (result: any) => {
        delete (window as any)[callbackName];
        document.body.removeChild(script);
        
        // 触发请求完成回调
        this.baseConfig.onResponse?.();
        
        resolve({
          success: true,
          data: result?.data?.result || result,
          message: result?.data?.content || '请求成功',
          code: result?.data?.code || 'SUCCESS'
        });
      };
      
      // 错误处理
      script.onerror = () => {
        delete (window as any)[callbackName];
        document.body.removeChild(script);
        
        // 触发请求完成和错误回调
        this.baseConfig.onResponse?.();
        this.baseConfig.onError?.(new Error('JSONP请求失败'));
        
        resolve({
          success: false,
          data: null,
          message: 'JSONP请求失败',
          code: 'ERROR'
        });
      };
      
      // 发送请求
      document.body.appendChild(script);
    });
  }
}

// 创建默认实例
export const http = HttpService.getInstance({
  baseURL: '',
  timeout: 10000
}); 