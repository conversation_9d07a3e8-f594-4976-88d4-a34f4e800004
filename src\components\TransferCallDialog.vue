<template>
  <el-dialog
    title="转接通话"
    v-model="dialogVisible"
    width="400px"
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <div class="transfer-container">
      <div class="current-call-info" v-if="currentCall">
        <div class="info-item">
          <span class="label">当前通话:</span>
          <span class="value">{{ currentCall.displayCustPhone }}</span>
        </div>
      </div>
      
      <el-tabs v-model="activeTab" class="transfer-tabs">
        <el-tab-pane label="转接坐席" name="agent">
          <el-form class="transfer-form" @submit.prevent="handleTransfer">
            <el-form-item label="坐席ID">
              <el-input v-model="transferForm.agentId" placeholder="请输入坐席ID"></el-input>
            </el-form-item>
            <el-form-item label="坐席分机">
              <el-input v-model="transferForm.agentPhone" placeholder="请输入坐席分机"></el-input>
            </el-form-item>
          </el-form>
          
          <div class="common-targets">
            <p class="subtitle">常用坐席</p>
            <el-row :gutter="10">
              <el-col :span="8" v-for="agent in commonAgents" :key="agent.id">
                <el-button size="small" @click="selectAgent(agent)">
                  {{ agent.name }} ({{ agent.phone }})
                </el-button>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="转接号码" name="number">
          <el-form class="transfer-form" @submit.prevent="handleTransfer">
            <el-form-item label="电话号码">
              <el-input v-model="transferForm.phoneNumber" placeholder="请输入转接号码"></el-input>
            </el-form-item>
            <el-form-item label="外显号码" v-if="showDisplayNumber">
              <el-input v-model="transferForm.displayNumber" placeholder="请输入外显号码"></el-input>
            </el-form-item>
          </el-form>
          
          <div class="common-targets">
            <p class="subtitle">常用号码</p>
            <el-row :gutter="10">
              <el-col :span="8" v-for="number in commonNumbers" :key="number.id">
                <el-button size="small" @click="selectNumber(number)">
                  {{ number.name }}
                </el-button>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>
      </el-tabs>
      
      <div class="transfer-options">
        <el-checkbox v-model="transferForm.isDirectTransfer">直接转接</el-checkbox>
        <el-tooltip content="直接转接: 不需要等待第三方应答，立即转接通话&#10;咨询转接: 与第三方通话后，再决定是否转接" placement="top">
          <el-icon class="help-icon"><question-filled /></el-icon>
        </el-tooltip>
      </div>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleTransfer" :loading="transferLoading">
          {{ transferForm.isDirectTransfer ? '直接转接' : '咨询转接' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch, reactive } from 'vue';
import { QuestionFilled } from '@element-plus/icons-vue';

export default defineComponent({
  name: 'transfer-call-dialog',
  components: {
    QuestionFilled
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    currentCall: {
      type: Object,
      default: null
    },
    showDisplayNumber: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'transfer'],
  setup(props, { emit }) {
    const dialogVisible = computed({
      get: () => props.modelValue,
      set: (value) => emit('update:modelValue', value)
    });
    
    const activeTab = ref('agent');
    const transferLoading = ref(false);
    
    // 转接表单
    const transferForm = reactive({
      agentId: '',
      agentPhone: '',
      phoneNumber: '',
      displayNumber: '',
      isDirectTransfer: true
    });
    
    // 常用坐席列表
    const commonAgents = ref([
      { id: 1, name: '技术支持', phone: '8002' },
      { id: 2, name: '售后服务', phone: '8003' },
      { id: 3, name: '投诉处理', phone: '8004' },
      { id: 4, name: '销售顾问', phone: '8005' },
      { id: 5, name: '团队主管', phone: '8006' },
      { id: 6, name: '客户经理', phone: '8007' }
    ]);
    
    // 常用号码列表
    const commonNumbers = ref([
      { id: 1, name: '技术部', phone: '01012345678' },
      { id: 2, name: '销售部', phone: '01012345679' },
      { id: 3, name: '客服部', phone: '01012345680' },
      { id: 4, name: '投诉热线', phone: '01012345681' },
      { id: 5, name: '紧急联系人', phone: '01012345682' },
      { id: 6, name: '前台', phone: '01012345683' }
    ]);
    
    // 选择坐席
    const selectAgent = (agent: any) => {
      transferForm.agentId = agent.name;
      transferForm.agentPhone = agent.phone;
    };
    
    // 选择号码
    const selectNumber = (number: any) => {
      transferForm.phoneNumber = number.phone;
    };
    
    // 处理转接
    const handleTransfer = async () => {
      transferLoading.value = true;
      
      try {
        // 根据当前选项卡确定转接目标
        const transferTarget = activeTab.value === 'agent' 
          ? { agentId: transferForm.agentId, agentPhone: transferForm.agentPhone }
          : { phoneNumber: transferForm.phoneNumber, displayNumber: transferForm.displayNumber };
        
        // 触发转接事件，传递转接信息
        emit('transfer', {
          ...transferTarget,
          isDirectTransfer: transferForm.isDirectTransfer
        });
        
        // 关闭对话框
        dialogVisible.value = false;
      } finally {
        transferLoading.value = false;
      }
    };
    
    // 对话框关闭后重置表单
    const handleClosed = () => {
      transferForm.agentId = '';
      transferForm.agentPhone = '';
      transferForm.phoneNumber = '';
      transferForm.displayNumber = '';
      transferForm.isDirectTransfer = true;
      activeTab.value = 'agent';
    };
    
    return {
      dialogVisible,
      activeTab,
      transferForm,
      transferLoading,
      commonAgents,
      commonNumbers,
      selectAgent,
      selectNumber,
      handleTransfer,
      handleClosed,
      showDisplayNumber: props.showDisplayNumber
    };
  }
});
</script>

<style scoped>
.transfer-container {
  padding: 10px 0;
}

.current-call-info {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.info-item {
  display: flex;
}

.label {
  font-weight: bold;
  width: 80px;
  color: #606266;
}

.value {
  flex: 1;
  color: #303133;
}

.transfer-tabs {
  margin-bottom: 15px;
}

.transfer-form {
  margin-bottom: 15px;
}

.common-targets {
  margin-top: 15px;
}

.subtitle {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.transfer-options {
  display: flex;
  align-items: center;
  margin-top: 15px;
  border-top: 1px solid #ebeef5;
  padding-top: 15px;
}

.help-icon {
  margin-left: 5px;
  font-size: 16px;
  color: #909399;
  cursor: pointer;
}
</style> 