/**
 * 全局状态管理模块
 * 统一管理整个系统的状态，确保状态一致性和稳定性
 */

import { EventEmitter } from './EventEmitter';
import { 
  AgentState, 
  AgentStateType, 
  FuncMaskType, 
  WorkModeType, 
  CCBarConfig,
  CallEvent 
} from '../types';
import { ccbarDebugger } from '../utils';

/**
 * 全局状态管理器类
 * 使用单例模式确保只有一个状态管理实例
 */
export class GlobalStateManager {
  private static instance: GlobalStateManager;
  private eventEmitter: EventEmitter;
  
  // 状态相关属性
  private sessionId: string = localStorage.getItem('sessionId') || '';
  private agentId: string = localStorage.getItem('agentId') || '';
  private phone: string = localStorage.getItem('phone') || '';
  private agentState: AgentState = {
    state: AgentStateType.LOGOFF,
    workMode: WorkModeType.ALL,
    stateDesc: '未登录',
    notifyContent: '',
    resultDesc: null,
    funcMask: {}
  };
  private config: CCBarConfig;
  private callInfo: CallEvent | null = null;
  private lastError: string | null = null;
  private stateHistory: Array<{timestamp: number, state: string, description: string}> = [];
  
  /**
   * 私有构造函数，防止直接实例化
   */
  private constructor(config: CCBarConfig = {}) {
    this.eventEmitter = EventEmitter.getInstance();
    this.config = config;
    
    // 注册错误处理
    this.setupErrorHandling();
    
    // 记录状态变更历史
    this.addStateHistory('初始化', '系统初始化');
    
    ccbarDebugger('全局状态管理器已初始化');
  }
  
  /**
   * 获取状态管理器实例
   */
  public static getInstance(config?: CCBarConfig): GlobalStateManager {
    if (!GlobalStateManager.instance) {
      GlobalStateManager.instance = new GlobalStateManager(config);
    } else if (config) {
      // 更新配置
      GlobalStateManager.instance.updateConfig(config);
    }
    return GlobalStateManager.instance;
  }
  
  /**
   * 更新配置
   */
  private updateConfig(config: CCBarConfig): void {
    this.config = { ...this.config, ...config };
    ccbarDebugger('全局状态配置已更新', this.config);
  }
  
  /**
   * 设置错误处理机制
   */
  private setupErrorHandling(): void {
    // 全局错误监听
    window.addEventListener('error', (event) => {
      const errorMsg = `全局错误: ${event.message} at ${event.filename}:${event.lineno}`;
      this.setLastError(errorMsg);
      ccbarDebugger(errorMsg, event.error, 'error');
      
      // 防止状态不一致
      this.validateState();
      return false; // 不阻止默认处理
    });
    
    // Promise 错误监听
    window.addEventListener('unhandledrejection', (event) => {
      const errorMsg = `未处理的Promise拒绝: ${event.reason}`;
      this.setLastError(errorMsg);
      ccbarDebugger(errorMsg, event.reason, 'error');
      
      // 防止状态不一致
      this.validateState();
      return false; // 不阻止默认处理
    });
  }
  
  /**
   * 获取默认功能掩码
   */
  private getDefaultFuncMask(): Record<string, boolean> {
    return {
      [FuncMaskType.LOGON]: true,
      [FuncMaskType.LOGOFF]: false,
      [FuncMaskType.MAKECALL]: false,
      [FuncMaskType.ANSWERCALL]: false,
      [FuncMaskType.CLEARCALL]: false,
      [FuncMaskType.HOLDCALL]: false,
      [FuncMaskType.UNHOLDCALL]: false,
      [FuncMaskType.TRANSFERCALL]: false,
      [FuncMaskType.CONSULTATIONCALL]: false,
      [FuncMaskType.CONFERENCECALL]: false,
      [FuncMaskType.AGENTREADY]: false,
      [FuncMaskType.AGENTNOTREADY]: false,
      [FuncMaskType.WORKREADY]: false,
      [FuncMaskType.CANCELCONSULTATION]: false,
      [FuncMaskType.COMPLETETRANSFER]: false,
    };
  }
  
  /**
   * 根据状态获取功能掩码
   */
  private getFuncMaskByState(state: string): Record<string, boolean> {
    const baseMask = this.getDefaultFuncMask();
    
    switch (state) {
      case AgentStateType.LOGOFF:
        return {
          ...baseMask,
          [FuncMaskType.LOGON]: true,
          [FuncMaskType.LOGOFF]: false
        };
        
      case AgentStateType.IDLE:
        return {
          ...baseMask,
          [FuncMaskType.LOGON]: false,
          [FuncMaskType.LOGOFF]: true,
          [FuncMaskType.MAKECALL]: true,
          [FuncMaskType.AGENTREADY]: false,
          [FuncMaskType.AGENTNOTREADY]: true
        };
        
      case AgentStateType.BUSY:
        return {
          ...baseMask,
          [FuncMaskType.LOGON]: false,
          [FuncMaskType.LOGOFF]: true,
          [FuncMaskType.MAKECALL]: true,
          [FuncMaskType.AGENTREADY]: true,
          [FuncMaskType.AGENTNOTREADY]: false
        };
        
      case AgentStateType.TALK:
        return {
          ...baseMask,
          [FuncMaskType.LOGON]: false,
          [FuncMaskType.LOGOFF]: false,
          [FuncMaskType.MAKECALL]: false,
          [FuncMaskType.CLEARCALL]: true,
          [FuncMaskType.HOLDCALL]: true,
          [FuncMaskType.TRANSFERCALL]: true,
          [FuncMaskType.CONSULTATIONCALL]: true,
          [FuncMaskType.MUTECALL]: true,
          [FuncMaskType.UNMUTECALL]: false
        };
        
      case AgentStateType.HELD:
        return {
          ...baseMask,
          [FuncMaskType.LOGON]: false,
          [FuncMaskType.LOGOFF]: false,
          [FuncMaskType.UNHOLDCALL]: true,
          [FuncMaskType.CLEARCALL]: true
        };
        
      case AgentStateType.WORKNOTREADY:
        return {
          ...baseMask,
          [FuncMaskType.LOGON]: false,
          [FuncMaskType.LOGOFF]: true,
          [FuncMaskType.WORKREADY]: true
        };
        
      case AgentStateType.CONSULTED:
        return {
          ...baseMask,
          [FuncMaskType.LOGON]: false,
          [FuncMaskType.LOGOFF]: false,
          [FuncMaskType.CLEARCALL]: true,
          [FuncMaskType.CANCELCONSULTATION]: true,
          [FuncMaskType.COMPLETETRANSFER]: true,
          [FuncMaskType.CONFERENCECALL]: true
        };
        
      default:
        return baseMask;
    }
  }
  
  /**
   * 添加状态历史记录
   */
  private addStateHistory(state: string, description: string): void {
    this.stateHistory.push({
      timestamp: Date.now(),
      state,
      description
    });
    
    // 限制历史记录长度，防止内存泄漏
    if (this.stateHistory.length > 100) {
      this.stateHistory.shift();
    }
  }
  
  /**
   * 设置最后错误信息
   */
  private setLastError(error: string): void {
    this.lastError = error;
    this.eventEmitter.emit('error', { error });
  }
  
  /**
   * 验证状态一致性
   * 用于错误恢复和状态自我修复
   */
  private validateState(): void {
    try {
      // 功能掩码一致性检查
      const expectedFuncMask = this.getFuncMaskByState(this.agentState.state);
      this.agentState.funcMask = {
        ...this.agentState.funcMask,
        ...expectedFuncMask
      };
      
      // 状态描述一致性检查
      this.agentState.stateDesc = this.getStateDesc(this.agentState.state);
      
      // 发出状态修复事件
      this.eventEmitter.emit('state:repaired', {
        previousState: this.getState(),
        currentState: this.getState(),
        timestamp: Date.now()
      });
      
      this.addStateHistory('状态修复', '自动修复状态一致性');
    } catch (error) {
      ccbarDebugger('状态验证过程中出错', error, 'error');
    }
  }
  
  /**
   * 获取状态描述
   */
  private getStateDesc(state: string): string {
    switch (state) {
      case AgentStateType.IDLE:
        return '空闲';
      case AgentStateType.BUSY:
        return '繁忙';
      case AgentStateType.ALERTING:
        return '振铃';
      case AgentStateType.TALK:
        return '通话中';
      case AgentStateType.HELD:
        return '保持';
      case AgentStateType.WORKNOTREADY:
        return '话后处理中';
      case AgentStateType.LOGOFF:
        return '未登录';
      case AgentStateType.CONSULTED:
        return '咨询';
      case AgentStateType.CONFERENCED:
        return '三方';
      case AgentStateType.MONITORED:
        return '监听';
      case AgentStateType.OCCUPY:
        return '预占';
      default:
        return '未知状态';
    }
  }
  
  /**
   * 设置代理状态
   */
  public setState(state: string, stateDesc?: string, notifyContent?: string): void {
    try {
      const oldState = {...this.agentState};
      
      // 更新状态
      this.agentState.state = state;
      this.agentState.stateDesc = stateDesc || this.getStateDesc(state);
      
      if (notifyContent !== undefined) {
        this.agentState.notifyContent = notifyContent;
      }
      
      // 更新功能掩码
      this.agentState.funcMask = this.getFuncMaskByState(state);
      
      // 记录状态变更
      this.addStateHistory(state, this.agentState.stateDesc);
      
      // 发出状态变更事件
      this.eventEmitter.emit('state:changed', {
        previousState: oldState,
        currentState: this.getState(),
        timestamp: Date.now()
      });
      
      // 为了兼容现有系统，也发出agent:stateChanged事件
      this.eventEmitter.emit('agent:stateChanged', {
        agentId: this.agentId,
        state: this.agentState.state,
        timestamp: Date.now()
      });
      
      // 发出agentStateSync事件，兼容现有代码
      this.eventEmitter.emit('agentStateSync', this.agentState);
      
      ccbarDebugger(`状态已更新: ${oldState.state} -> ${state}`);
    } catch (error) {
      ccbarDebugger('设置状态时出错', error, 'error');
      this.setLastError(`设置状态时出错: ${error}`);
    }
  }
  
  /**
   * 更新通话信息
   */
  public updateCallInfo(callInfo: CallEvent | null, updateState: boolean = true): void {
    try {
      this.callInfo = callInfo;
      
      if (updateState && callInfo) {
        // 通话状态更新
        switch (callInfo.event.createCause) {
          case 'alerting':
            this.setState(AgentStateType.ALERTING, '振铃中');
            break;
          case 'connected':
            this.setState(AgentStateType.TALK, '通话中');
            break;
          case 'held':
            this.setState(AgentStateType.HELD, '通话保持');
            break;
        }
      } else if (updateState && !callInfo) {
        // 通话结束，回到未就绪状态
        this.setState(AgentStateType.WORKNOTREADY, '话后整理');
      }
      
      ccbarDebugger('通话信息已更新', callInfo);
    } catch (error) {
      ccbarDebugger('更新通话信息时出错', error, 'error');
      this.setLastError(`更新通话信息时出错: ${error}`);
    }
  }
  
  /**
   * 更新工作模式
   */
  public updateWorkMode(workMode: string): void {
    try {
      this.agentState.workMode = workMode;
      
      // 发出工作模式变更事件
      this.eventEmitter.emit('workMode:changed', {
        workMode,
        timestamp: Date.now()
      });
      
      // 发出状态同步事件
      this.eventEmitter.emit('agentStateSync', this.agentState);
      
      ccbarDebugger(`工作模式已更新: ${workMode}`);
    } catch (error) {
      ccbarDebugger('更新工作模式时出错', error, 'error');
      this.setLastError(`更新工作模式时出错: ${error}`);
    }
  }
  
  /**
   * 获取当前状态
   */
  public getState(): AgentState {
    return { ...this.agentState };
  }
  
  /**
   * 获取当前状态类型
   */
  public getStateType(): string {
    return this.agentState.state;
  }
  
  /**
   * 获取当前登录状态
   * 根据状态判断是否登录，LOGOFF表示未登录，其他状态表示已登录
   */
  public isLoggedIn(): boolean {
    return this.agentState.state !== AgentStateType.LOGOFF;
  }
  
  /**
   * 获取会话ID
   */
  public getSessionId(): string {
    return this.sessionId || localStorage.getItem('sessionId') || '';
  }
  
  /**
   * 获取座席ID
   */
  public getAgentId(): string {
    return this.agentId || localStorage.getItem('agentId') || '';
  }
  
  /**
   * 获取话机号码
   */
  public getPhone(): string {
    return this.phone || localStorage.getItem('phone') || '';
  }
  
  /**
   * 设置话机号码
   */
  public setPhone(phone: string): void {
    this.phone = phone;
    if (phone) {
      localStorage.setItem('phone', phone);
    }
    ccbarDebugger('话机号码已更新', phone);
  }
  
  /**
   * 获取通话信息
   */
  public getCallInfo(): CallEvent | null {
    return this.callInfo;
  }
  
  /**
   * 获取最后错误
   */
  public getLastError(): string | null {
    return this.lastError;
  }
  
  /**
   * 获取状态历史
   */
  public getStateHistory(): Array<{timestamp: number, state: string, description: string}> {
    return [...this.stateHistory];
  }
  
  /**
   * 获取功能掩码
   */
  public getFuncMask(): Record<string, boolean> {
    return { ...this.agentState.funcMask };
  }
  
  /**
   * 检查功能是否可用
   */
  public isFuncEnabled(funcName: FuncMaskType): boolean {
    return !!this.agentState.funcMask[funcName];
  }
  
  /**
   * 注册状态变更监听器
   */
  public onStateChanged(callback: (event: any) => void): void {
    this.eventEmitter.on('state:changed', callback);
  }
  
  /**
   * 注册代理状态同步监听器
   */
  public onAgentStateSync(callback: (state: AgentState) => void): void {
    this.eventEmitter.on('agentStateSync', callback);
  }
  
  /**
   * 注册错误监听器
   */
  public onError(callback: (event: { error: string }) => void): void {
    this.eventEmitter.on('error', callback);
  }
  
  /**
   * 移除状态变更监听器
   */
  public offStateChanged(callback: (event: any) => void): void {
    this.eventEmitter.off('state:changed', callback);
  }
  
  /**
   * 移除代理状态同步监听器
   */
  public offAgentStateSync(callback: (state: AgentState) => void): void {
    this.eventEmitter.off('agentStateSync', callback);
  }
  
  /**
   * 移除错误监听器
   */
  public offError(callback: (event: { error: string }) => void): void {
    this.eventEmitter.off('error', callback);
  }
  
  /**
   * 重置状态
   * 用于系统初始化或彻底重置
   */
  public resetState(): void {
    this.sessionId = '';
    this.agentId = '';
    this.phone = '';
    this.callInfo = null;
    this.lastError = null;
    
    this.agentState = {
      state: AgentStateType.LOGOFF,
      workMode: WorkModeType.ALL,
      stateDesc: '未登录',
      notifyContent: '',
      resultDesc: null,
      funcMask: this.getDefaultFuncMask()
    };
    
    this.stateHistory = [];
    this.addStateHistory('重置', '状态已重置');
    
    // 发出状态重置事件
    this.eventEmitter.emit('state:reset', {
      state: this.getState(),
      timestamp: Date.now()
    });
    
    // 触发状态同步
    this.eventEmitter.emit('agentStateSync', this.agentState);
    
    ccbarDebugger('状态已重置');
  }
}

// 导出单例实例
export default GlobalStateManager; 