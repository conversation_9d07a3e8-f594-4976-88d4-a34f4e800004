import { AgentStateType, CCBarResponse } from '../../types';

/**
 * 状态管理器接口
 * 负责管理座席状态
 */
export interface IStateManager {
  /**
   * 更新会话ID
   * @param sessionId 新的会话ID
   */
  updateSessionId(sessionId: string): void;
  
  /**
   * 设置座席状态为就绪
   * @returns 设置结果
   */
  setReady(): Promise<CCBarResponse>;
  
  /**
   * 设置座席状态为未就绪
   * @returns 设置结果
   */
  setNotReady(): Promise<CCBarResponse>;
  
  /**
   * 设置座席状态
   * @param state 目标状态
   * @returns 设置结果
   */
  setState(state: AgentStateType): Promise<CCBarResponse>;
  
  /**
   * 获取当前状态
   * @returns 当前状态
   */
  getCurrentState(): AgentStateType;
  
  /**
   * 更新当前状态（通常由事件触发）
   * @param state 新状态
   */
  updateCurrentState(state: AgentStateType): void;
} 