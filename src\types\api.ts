import { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

/**
 * HTTP服务配置接口
 */
export interface HttpServiceConfig {
  /** API基础URL */
  baseURL: string;
  /** 请求超时时间（毫秒） */
  timeout?: number;
  /** 请求头 */
  headers?: Record<string, string>;
  /** 会话ID */
  sessionId?: string;
  /** 请求开始前回调 */
  onRequest?: () => void;
  /** 请求响应后回调 */
  onResponse?: () => void;
  /** 请求错误回调 */
  onError?: (error: any) => void;
  /** 会话超时回调 */
  onSessionTimeout?: () => void;
}

/**
 * 请求选项接口，扩展自AxiosRequestConfig
 */
export type RequestOptions = AxiosRequestConfig;

/**
 * API响应接口
 */
export interface ApiResponse<T = any> {
  /** 请求是否成功 */
  success: boolean;
  /** 响应数据 */
  data: T | null;
  /** 响应消息 */
  message: string;
  /** 错误代码 */
  errorCode?: string;
  /** 响应代码 */
  code?: string;
  /** 原始响应对象 */
  originalResponse?: AxiosResponse;
  /** 原始错误对象 */
  originalError?: AxiosError;
}

/**
 * WebSocket配置接口
 */
export interface WebSocketConfig {
  /** WebSocket URL */
  url: string;
  /** WebSocket协议 */
  protocols?: string | string[];
  /** 自动重连 */
  autoReconnect?: boolean;
  /** 最大重连次数 */
  maxReconnectAttempts?: number;
  /** 重连间隔（毫秒） */
  reconnectInterval?: number;
  /** 心跳间隔（毫秒） */
  heartbeatInterval?: number;
  /** 心跳消息 */
  heartbeatMessage?: string | object;
  /** 连接成功回调 */
  onOpen?: (event: Event) => void;
  /** 连接关闭回调 */
  onClose?: (event: CloseEvent) => void;
  /** 收到消息回调 */
  onMessage?: (data: any) => void;
  /** 连接错误回调 */
  onError?: (event: Event) => void;
  /** 会话超时回调 */
  onSessionTimeout?: () => void;
}

/**
 * 轮询配置接口
 */
export interface PollingConfig {
  /** API基础URL */
  baseURL: string;
  /** 轮询间隔（毫秒） */
  pollingInterval: number;
  /** 轮询URL */
  pollingUrl: string;
  /** 会话ID */
  sessionId?: string;
  /** 请求参数 */
  params: Record<string, any>;
  /** 请求头 */
  headers: Record<string, string>;
  /** 最大空结果次数 */
  maxEmptyResults?: number;
  /** 自动开始轮询 */
  autoStart?: boolean;
  /** 轮询请求方法 */
  method?: 'GET' | 'POST';
  /** 轮询超时时间（毫秒） */
  timeout?: number;
  /** 收到数据回调 */
  onData?: (data: any) => void;
  /** 收到事件回调 */
  onEvents?: (events: any[]) => void;
  /** 收到响应回调 */
  onResponse?: (response: any) => void;
  /** 轮询错误回调 */
  onError?: (error: any) => void;
  /** 会话超时回调 */
  onSessionTimeout?: () => void;
} 