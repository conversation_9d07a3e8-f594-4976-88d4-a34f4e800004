import { CCBarConfig } from '../types';

/**
 * 默认配置
 */
const defaultConfig: CCBarConfig = {
  baseURL: '/api/yc-ccbar-v1',
  wsURL: '/ws',
  timeout: 10000,
  autoReconnect: true,
  maxReconnectAttempts: 5,
  reconnectInterval: 3000,
  heartbeatInterval: 30000,
  autoReady: false,
  pollingInterval: 5000,
  entId: '',
  loginKey: '',
  productId: '',
  debug: true
};

/**
 * 从localStorage加载配置
 * @returns 加载的配置
 */
export function loadConfig(): CCBarConfig {
  try {
    // 首先尝试从localStorage加载
    const savedConfig = localStorage.getItem('ccbarConfig');
    if (savedConfig) {
      // 合并默认配置和保存的配置
      return { ...defaultConfig, ...JSON.parse(savedConfig) };
    }

    // 如果没有保存的配置，检查全局window.ccbarConfig
    if (typeof window !== 'undefined' && window.ccbarConfig) {
      return {
        ...defaultConfig,
        baseURL: window.ccbarConfig.apiBaseUrl || defaultConfig.baseURL,
        wsURL: window.ccbarConfig.wsUrl || defaultConfig.wsURL
      };
    }
  } catch (error) {
    console.error('加载配置失败:', error);
  }

  // 如果都没有，返回默认配置
  return { ...defaultConfig };
}

/**
 * 使用提供的配置初始化系统配置
 * @param config 外部提供的配置
 * @returns 处理后的配置
 */
export function initializeWithConfig(config: CCBarConfig): CCBarConfig {
  // 合并默认配置和传入的配置
  const mergedConfig = { ...defaultConfig, ...config };
  
  // 保存到localStorage
  saveConfig(mergedConfig);
  
  // 更新全局配置
  updateGlobalConfig(mergedConfig);
  
  console.log('[Config] 系统使用的配置:', mergedConfig);
  
  return mergedConfig;
}

/**
 * 保存配置到localStorage
 * @param config 要保存的配置
 */
export function saveConfig(config: CCBarConfig): void {
  try {
    localStorage.setItem('ccbarConfig', JSON.stringify(config));
    
    // 同时更新全局配置
    updateGlobalConfig(config);
  } catch (error) {
    console.error('保存配置失败:', error);
  }
}

/**
 * 更新全局window.ccbarConfig
 * @param config 配置对象
 */
export function updateGlobalConfig(config: CCBarConfig): void {
  if (typeof window !== 'undefined') {
    window.ccbarConfig = {
      apiBaseUrl: config.baseURL,
      wsUrl: config.wsURL
    };
  }
}

/**
 * 合并配置对象
 * @param userConfig 用户提供的配置
 * @returns 合并后的配置
 */
export function mergeConfig(userConfig?: Partial<CCBarConfig>): CCBarConfig {
  // 加载已保存的配置
  const loadedConfig = loadConfig();
  
  // 合并用户提供的配置
  if (userConfig) {
    return { ...loadedConfig, ...userConfig };
  }
  
  return loadedConfig;
}

/**
 * 配置类型扩展
 */
declare global {
  interface Window {
    ccbarConfig?: {
      apiBaseUrl?: string;
      wsUrl?: string;
    };
  }
} 