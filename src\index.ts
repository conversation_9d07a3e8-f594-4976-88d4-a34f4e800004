import { App } from 'vue';
import CCBarComponent from './components/CCBar.vue';
import CCBarToolbarComponent from './components/CCBarToolbar.vue';
import CCBarLoginComponent from './components/CCBarLogin.vue';
import { CCBarService } from './core/CCBarService';
import { CCBarConfig } from './types';

// 扩展CCBar类型声明，添加service属性和常用方法
interface CCBarWithService {
  install: (app: App, options?: CCBarConfig) => void;
  service?: CCBarService;
  name: string;
  version: string;
  CCBarComponent: typeof CCBarComponent;
  CCBarToolbar: typeof CCBarToolbarComponent;
  CCBarLogin: typeof CCBarLoginComponent;
  getInstance: (config: CCBarConfig) => CCBarService;
  
  // 添加常用方法
  login: (params: any) => Promise<any>;
  logout: () => Promise<any>;
  agentReady: () => Promise<any>;
  agentNotReady: (busyType?: string) => Promise<any>;
  makeCall: (phoneNumber: string, displayNumber?: string, userData?: any, callType?: number) => Promise<any>;
  answerCall: () => Promise<any>;
  clearCall: (callData?: any) => Promise<any>;
  holdCall: () => Promise<any>;
  unholdCall: () => Promise<any>;
  getState: () => string;
  getAgentInfo: () => any;
  on: (eventName: string, callback: Function) => void;
  off: (eventName: string, callback: Function) => void;
}

// 创建带service的CCBar导出对象
const CCBar: CCBarWithService = {
  install(app: App, options?: CCBarConfig) {
    // 注册组件
    app.component('CCBar', CCBarComponent);
    app.component('CCBarToolbar', CCBarToolbarComponent);
    app.component('CCBarLogin', CCBarLoginComponent);

    // 确保也注册kebab-case版本的标签
    app.component('cc-bar', CCBarComponent);
    app.component('cc-bar-toolbar', CCBarToolbarComponent);
    app.component('cc-bar-login', CCBarLoginComponent);

    // 如果提供了配置，初始化服务
    if (options && options.baseURL) {
      const service = CCBarService.getInstance(options);
      // @ts-ignore - 添加service属性
      this.service = service;
    }
  },
  name: 'CCBar',
  version: '1.0.0',
  CCBarComponent,
  CCBarToolbar: CCBarToolbarComponent,
  CCBarLogin: CCBarLoginComponent,
  
  // 添加getInstance方法，直接代理到CCBarService.getInstance
  getInstance: (config: CCBarConfig) => {
    return CCBarService.getInstance(config);
  },
  
  // 添加常用方法，全部代理到service实例
  login: (params: any) => {
    if (!CCBar.service) {
      console.error('[CCBar] 在调用login前必须先初始化service实例');
      throw new Error('CCBar service not initialized');
    }
    return CCBar.service.login(params);
  },
  
  logout: () => {
    if (!CCBar.service) {
      console.error('[CCBar] 在调用logout前必须先初始化service实例');
      throw new Error('CCBar service not initialized');
    }
    return CCBar.service.logout();
  },
  
  agentReady: () => {
    if (!CCBar.service) {
      console.error('[CCBar] 在调用agentReady前必须先初始化service实例');
      throw new Error('CCBar service not initialized');
    }
    return CCBar.service.agentReady();
  },
  
  agentNotReady: (busyType?: string) => {
    if (!CCBar.service) {
      console.error('[CCBar] 在调用agentNotReady前必须先初始化service实例');
      throw new Error('CCBar service not initialized');
    }
    return CCBar.service.agentNotReady(busyType);
  },
  
  makeCall: (phoneNumber: string, displayNumber?: string, userData: any = {}, callType: number = 2) => {
    if (!CCBar.service) {
      console.error('[CCBar] 在调用makeCall前必须先初始化service实例');
      throw new Error('CCBar service not initialized');
    }
    return CCBar.service.makeCall(phoneNumber, displayNumber, userData, callType);
  },
  
  answerCall: () => {
    if (!CCBar.service) {
      console.error('[CCBar] 在调用answerCall前必须先初始化service实例');
      throw new Error('CCBar service not initialized');
    }
    return CCBar.service.answerCall();
  },
  
  clearCall: (callData?: any) => {
    if (!CCBar.service) {
      console.error('[CCBar] 在调用clearCall前必须先初始化service实例');
      throw new Error('CCBar service not initialized');
    }
    return CCBar.service.clearCall(callData);
  },
  
  holdCall: () => {
    if (!CCBar.service) {
      console.error('[CCBar] 在调用holdCall前必须先初始化service实例');
      throw new Error('CCBar service not initialized');
    }
    return CCBar.service.holdCall();
  },
  
  unholdCall: () => {
    if (!CCBar.service) {
      console.error('[CCBar] 在调用unholdCall前必须先初始化service实例');
      throw new Error('CCBar service not initialized');
    }
    return CCBar.service.unholdCall();
  },
  
  getState: () => {
    if (!CCBar.service) {
      console.error('[CCBar] 在调用getState前必须先初始化service实例');
      throw new Error('CCBar service not initialized');
    }
    return CCBar.service.getState();
  },
  
  getAgentInfo: () => {
    if (!CCBar.service) {
      console.error('[CCBar] 在调用getAgentInfo前必须先初始化service实例');
      throw new Error('CCBar service not initialized');
    }
    return CCBar.service.getAgentInfo();
  },
  
  on: (eventName: string, callback: Function) => {
    if (!CCBar.service) {
      console.error('[CCBar] 在调用on前必须先初始化service实例');
      throw new Error('CCBar service not initialized');
    }
    CCBar.service.on(eventName, callback);
  },
  
  off: (eventName: string, callback: Function) => {
    if (!CCBar.service) {
      console.error('[CCBar] 在调用off前必须先初始化service实例');
      throw new Error('CCBar service not initialized');
    }
    CCBar.service.off(eventName, callback);
  }
};

// 组件
export { CCBarComponent };
export { CCBarToolbarComponent as CCBarToolbar };
export { CCBarLoginComponent as CCBarLogin };

// 导出带service的CCBar
export { CCBar };

// 服务
export { CCBarService };

// 类型
export * from './types';

// 工具函数
export { 
  ccbarDebugger, 
  tipsMsg, 
  debounce, 
  throttle,
  replacePhoneNum,
  formatDate
} from './utils';

// 确保在浏览器环境下设置全局变量
if (typeof window !== 'undefined') {
  // @ts-ignore
  window.CCBar = CCBar;
}

// 默认导出
export default CCBar; 