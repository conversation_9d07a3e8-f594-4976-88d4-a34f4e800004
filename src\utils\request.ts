import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ccbarDebugger } from './index';
import { loadConfig } from './config';
import { ElMessage } from 'element-plus';
import { EventEmitter } from '../core/EventEmitter';

/**
 * HTTP请求服务类
 * 负责发送HTTP请求并处理响应
 */
export class RequestService {
  private axiosInstance: AxiosInstance;
  private debug: boolean;

  /**
   * 构造函数
   * @param timeout 请求超时时间（毫秒）
   * @param debug 是否开启调试模式
   */
  constructor(timeout: number = 10000, debug: boolean = false) {
    this.debug = debug;

    // 从全局配置获取baseURL
    const config = loadConfig();
    const baseURL = config.baseURL || '';

    this.axiosInstance = axios.create({
      baseURL,
      timeout,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      }
    });

    if (this.debug) {
      ccbarDebugger(`初始化RequestService，baseURL: ${baseURL}`);
    }

    // 请求拦截器
    this.axiosInstance.interceptors.request.use(
      (config) => {
        if (this.debug) {
          ccbarDebugger(`请求: ${config.method?.toUpperCase()} ${config.url}`, config.data || config.params);
        }
        return config;
      },
      (error) => {
        if (this.debug) {
          ccbarDebugger('请求错误', error);
        }
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.axiosInstance.interceptors.response.use(
      (response) => {
        if (this.debug) {
          ccbarDebugger(`响应: ${response.status}`, response.data);
        }
        if (response.data.state == 1) {

          return response.data;
        } else {
          if (response.data.data.resultCode == '403') {
            // 检测到403错误码，表示会话过期或权限错误，需要执行签出操作
            console.log('检测到403错误码，立即停止轮询和心跳操作');

            sessionStorage.setItem('isLogined', 'false');
            // 触发403错误事件，由组件监听并执行签出
            const eventEmitter = EventEmitter.getInstance();

            // 立即停止轮询和心跳
            eventEmitter.emit('system:stopPolling', {
              reason: '403 Forbidden - 会话已过期或无效',
              timestamp: new Date().getTime(),
              stopPolling: true
            });

            // 触发403错误事件，由组件监听并执行签出
            eventEmitter.emit('session:403error', {
              message: '会话已过期，需要重新登录',
              timestamp: new Date().getTime(),
              stopPolling: true // 标记需要停止轮询
            });


            // 重要：对于403错误，我们不要继续处理请求，而是直接返回错误
            return Promise.reject({
              state: false,
              msg: '会话已过期，需要重新登录',
              data: {
                code: '403',
                content: '会话已过期，需要重新登录'
              }
            });
          } else {
            ElMessage({
              message: response.data.data.content,
              type: 'error',
              plain: true
            })
          }
          return response.data
        }
      },
      (error) => {
        if (this.debug) {
          ccbarDebugger('响应错误', error);
        }

        // 构造统一的错误响应格式
        const response = {
          state: false,
          msg: error.message || '请求失败',
          data: {
            code: error.response?.status || 'error',
            content: error.message || '未知错误'
          }
        };

        return Promise.reject(response);
      }
    );
  }

  /**
   * 发送GET请求
   * @param url 请求URL
   * @param params 请求参数
   * @param config 其他配置
   * @returns 请求响应
   */
  public async get(url: string, params?: any, config?: AxiosRequestConfig): Promise<any> {
    try {
      return await this.axiosInstance.get(url, { params, ...config });
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * 发送POST请求
   * @param url 请求URL
   * @param data 请求数据
   * @param config 其他配置
   * @returns 请求响应
   */
  public async post(url: string, data?: any, config?: AxiosRequestConfig): Promise<any> {
    try {
      // 支持原始ccbar.js的JSONP格式
      let jsonpConfig = {};
      // if (url.includes('cx-ccbar-12345/AgentEvent') || url.includes('cx-ccbar-12345/callEvent')) {
      //   const callbackName = 'jsonpCallback' + Math.floor(Math.random() * 1000000);
      //   jsonpConfig = {
      //     ...config,
      //     params: {
      //       ...(config?.params || {}),
      //       callbackFunc: callbackName
      //     }
      //   };

      //   // 使用JSONP方式请求
      //   return await this.jsonp(url, data, callbackName);
      // }

      return await this.axiosInstance.post(url, data, { ...config, ...jsonpConfig });
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * 发送PUT请求
   * @param url 请求URL
   * @param data 请求数据
   * @param config 其他配置
   * @returns 请求响应
   */
  public async put(url: string, data?: any, config?: AxiosRequestConfig): Promise<any> {
    try {
      return await this.axiosInstance.put(url, data, config);
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * 发送DELETE请求
   * @param url 请求URL
   * @param config 其他配置
   * @returns 请求响应
   */
  public async delete(url: string, config?: AxiosRequestConfig): Promise<any> {
    try {
      return await this.axiosInstance.delete(url, config);
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * 处理请求错误
   * @param error 错误信息
   * @returns 统一的错误响应
   */
  private handleError(error: any): any {
    if (error.state === false) {
      // 已经是我们格式化的错误，直接返回
      return error;
    }

    return {
      state: false,
      msg: error.message || '请求失败',
      data: {
        code: 'error',
        content: error.message || '未知错误'
      }
    };
  }

  /**
   * 使用JSONP方式发送请求
   * @param url 请求URL
   * @param data 请求数据
   * @param callbackName 回调函数名
   * @returns Promise<any>
   */
  private jsonp(url: string, data: any, callbackName: string): Promise<any> {
    return new Promise((resolve, reject) => {
      try {
        // 创建script标签
        const script = document.createElement('script');
        const fullUrl = this.axiosInstance.defaults.baseURL + url;

        // 构建查询字符串
        const params = new URLSearchParams();
        for (const key in data) {
          if (data.hasOwnProperty(key)) {
            params.append(key, data[key]);
          }
        }
        params.append('callbackFunc', callbackName);

        // 设置完整URL
        const separator = fullUrl.includes('?') ? '&' : '?';
        script.src = `${fullUrl}${separator}${params.toString()}`;

        // 定义全局回调函数
        (window as any)[callbackName] = (response: any) => {
          // 请求完成后，清理
          document.body.removeChild(script);
          delete (window as any)[callbackName];

          // 处理响应
          if (response.state === undefined) {
            // 适配原始ccbar.js的响应格式
            resolve({
              state: response.data?.code === 'succ' || response.data?.resultCode === '000',
              msg: response.data?.content || '',
              data: response.data
            });
          } else {
            resolve(response);
          }
        };

        // 处理加载错误
        script.onerror = (error) => {
          document.body.removeChild(script);
          delete (window as any)[callbackName];
          reject({
            state: false,
            msg: '请求失败',
            data: {
              code: 'error',
              content: '网络请求失败'
            }
          });
        };

        // 添加到文档中开始请求
        document.body.appendChild(script);
      } catch (error) {
        reject({
          state: false,
          msg: error instanceof Error ? error.message : String(error),
          data: {
            code: 'error',
            content: '未知错误'
          }
        });
      }
    });
  }
}

// 创建默认实例
export const request = new RequestService(5000, true); 