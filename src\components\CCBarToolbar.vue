<!--
CCBarToolbar.vue - CCBar话务条组件

这是一个轻量级的话务条UI组件，可以嵌入到任何第三方应用中，提供呼叫中心的核心功能：
1. 座席状态显示和控制（置闲/置忙/小休等）
2. 呼叫控制（拨号、挂断、保持、恢复等）
3. 简洁的UI设计，适合嵌入到各种应用界面

使用方法：
1. 引入组件并注册
   ```javascript
   import CCBarToolbar from 'path/to/CCBarToolbar.vue';
   
   export default {
     components: {
       CCBarToolbar
     }
   }
   ```

2. 在模板中使用
   ```html
   <CCBarToolbar 
     :config="ccbarConfig"
     @login-success="handleLoginSuccess"
     @state-changed="handleStateChanged"
     @call-event="handleCallEvent"
   />
   ```

3. 配置参数示例
   ```javascript
   const ccbarConfig = {
     apiBaseUrl: '/api',  // API基础地址
     wsUrl: '/ws',        // WebSocket地址
     entId: '123456',     // 企业ID
     loginKey: 'xxxxxxxx', // 登录密钥
     productId: '100000'   // 产品ID
   }
   ```
-->

<template>
  <div
    class="ccbar-toolbar"
    :class="{ 'is-expanded': expanded }"
    :key="forceUpdateCounter"
  >
    <!-- 折叠/展开按钮 -->
    <!-- <div class="toolbar-toggle" @click="toggleExpand">
      <span v-if="expanded">《</span>
      <span v-else>》</span>
    </div> -->

    <!-- 主工具栏 -->
    <div class="toolbar-main">
      <!-- 状态信息区 -->
      <div class="toolbar-status">
        <div class="agent-info">
          <div class="agent-state" :class="getStateClass()">
            <i class="state-icon"></i>
            <span>{{
              !isLoggedIn ? "登出" : agentState.stateDesc || "未知状态"
            }}</span>
          </div>
          <div class="agent-state-duration">
            <span>持续: {{ stateDurationText }}</span>
          </div>
          <div v-if="isLoggedIn" class="agent-id">
            <span>工号: {{ agentId }}</span>
          </div>
          <div
            v-if="isLoggedIn && outboundNumber"
            class="agent-outbound-number"
          >
            <span>外显号码: {{ outboundNumber }}</span>
          </div>
        </div>

        <!-- 状态控制按钮组 - 仅在登录状态下显示 -->
        <div v-if="isLoggedIn" class="state-controls">
          <button
            class="btn btn-ready"
            :disabled="!canSetReady"
            @click="setAgentReady"
          >
            置闲
          </button>
          <button
            class="btn btn-not-ready"
            :disabled="!canSetNotReady"
            @click="setAgentNotReady"
          >
            置忙
          </button>
        </div>
      </div>

      <!-- 呼叫控制区 - 仅在登录状态下显示 -->
      <div v-if="isLoggedIn" class="toolbar-call">
        <div class="call-input">
          <input
            type="text"
            v-model="phoneNumber"
            placeholder="请输入电话号码"
          />
          <button
            class="btn btn-call"
            :disabled="!phoneNumber || !canMakeCall"
            @click="makeCall"
          >
            拨打
          </button>
        </div>

        <div class="call-controls">
          <!-- 挂断按钮始终显示 -->
          <button
            class="btn btn-hangup"
            :disabled="!canHangupCall"
            @click="hangupCall"
          >
            挂断
          </button>
          <!-- 保持按钮在非静音状态且非保持状态下显示 -->
          <button
            v-if="agentState.state !== AgentStateType.HELD && !isMuted"
            class="btn btn-hold"
            :disabled="!canHoldCall"
            @click="holdCall"
          >
            保持
          </button>
          <!-- 恢复按钮在保持状态下显示，并且不在静音状态下才显示 -->
          <button
            v-if="agentState.state === AgentStateType.HELD"
            class="btn btn-retrieve"
            :disabled="!canRetrieveCall"
            @click="retrieveCall"
          >
            恢复
          </button>
          <!-- 静音按钮，仅在非静音且非保持状态下显示 -->
          <button
            v-if="agentState.state !== AgentStateType.MUTE"
            class="btn btn-mute"
            :disabled="!canMuteCall"
            @click="muteCall"
          >
            静音
          </button>
          <!-- 取消静音按钮，仅在静音状态下显示 -->
          <button
            v-if="agentState.state === AgentStateType.MUTE"
            class="btn btn-unmute"
            :disabled="!canUnmuteCall"
            @click="unmuteCall"
          >
            取消静音
          </button>
        </div>
      </div>

      <!-- 登录状态区 - 根据登录状态显示不同按钮 -->
      <div v-if="!isLoggedIn" class="toolbar-login">
        <button class="btn btn-login" @click="$emit('show-login')">签入</button>
      </div>
      <div v-else class="toolbar-logout">
        <button
          class="btn btn-logout"
          :disabled="!canLogout"
          @click="onLogoutButtonClick"
        >
          签出
        </button>
      </div>
    </div>

    <!-- 最近通知区 -->
    <div v-if="expanded && latestEvent" class="toolbar-notification">
      <div class="event-item">
        <span class="event-type">{{ latestEvent.type }}</span>
        <span class="event-content">{{ latestEvent.content }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  computed,
  onMounted,
  onBeforeUnmount,
  watch,
  nextTick,
} from "vue";
import { CCBarService } from "../core/CCBarService";
import GlobalStateManager from "../core/GlobalStateManager";
import { AgentStateType } from "../types";
import { EventEmitter } from "../core/EventEmitter";
import { EventManager } from "../core/modules/EventManager";
import { ElMessage } from "element-plus";

// 定义组件属性
const props = defineProps({
  config: {
    type: Object,
    required: true,
  },
});

// 定义组件事件
const emit = defineEmits([
  "show-login",
  "login-success",
  "logout-success",
  "state-changed",
  "call-event",
  "init-complete",
]);

// 状态变量
const expanded = ref(true);
const showStateMenu = ref(false);
const phoneNumber = ref("");
const outboundNumber = ref("");
const latestEvent = ref<{ time: string; type: string; content: string } | null>(
  null
);
const stateStartTime = ref(new Date());
const stateDuration = ref(0);
const stateTimer = ref<number | null>(null);
const isInitialized = ref(false);
// 当前通话数据 - 用于存储通话相关信息
const currentCallData = ref<any>(null);

// 获取全局状态管理器实例
const globalStateManager = GlobalStateManager.getInstance();

// 获取CCBarService实例
// 注意：这里不使用const，而是let，这样可以在需要时重新初始化
let ccbarService = CCBarService.getInstance(props.config);

// 确保使用全局单例事件总线
const eventEmitter = EventEmitter.getInstance();

// 创建事件管理器，传入全局事件总线
const eventManager = new EventManager(eventEmitter);

// 登录状态 - 使用ref而不是computed以便更直接的控制
const isLoggedInFromState = computed(() => globalStateManager.isLoggedIn());
const isLoggedIn = ref(false); // 使用ref，方便显式更新
const agentId = computed(() => globalStateManager.getAgentId()); // 保留座席ID计算属性

// 座席状态
const agentState = reactive({
  state: AgentStateType.LOGOFF,
  stateDesc: "登出",
  funcMask: {} as Record<string, boolean>,
});

// 是否已静音状态
const isMuted = ref(false);

// 按钮状态
const canMakeCall = computed(() => agentState.funcMask?.makecall || false);
const canSetReady = computed(() => agentState.funcMask?.agentready || false);
const canSetNotReady = computed(
  () => agentState.funcMask?.agentnotready || false
);
const canHangupCall = computed(() => {
  // 默认使用funcMask
  const fromFuncMask = agentState.funcMask?.clearcall || false;

  // 但在HELD状态或静音状态或TALK状态下，无论funcMask如何，都允许挂断
  if (
    agentState.state === AgentStateType.HELD ||
    isMuted.value ||
    agentState.state === AgentStateType.TALK
  ) {
    return true;
  }

  return fromFuncMask;
});
const canHoldCall = computed(() => {
  // 在静音状态或保持状态下不允许保持
  if (isMuted.value || agentState.state === AgentStateType.HELD) {
    return false;
  }

  return agentState.funcMask?.holdcall || false;
});
const canRetrieveCall = computed(() => {
  // 优先检查HELD状态
  if (agentState.state === AgentStateType.HELD) {
    console.log("HELD状态，强制启用恢复按钮");
    return true;
  }
  // 默认使用funcMask
  return agentState.funcMask?.unholdcall || false;
});
const canMuteCall = computed(() => {
  // 在保持状态下不允许静音
  if (agentState.state === AgentStateType.HELD) {
    return false;
  }

  // 只有在通话状态下才能静音
  if (agentState.state === AgentStateType.TALK) {
    return true;
  }

  return agentState.funcMask?.mutecall || false;
});
const canUnmuteCall = computed(() => {
  // 只有在已静音状态下才能取消静音
  if (agentState.state === AgentStateType.MUTE) {
    return true;
  }

  // 静音状态下总是可以取消静音
  return agentState.funcMask?.unMutecall || false;
});
const canLogout = computed(() => agentState.funcMask?.logoff || false);

// 登录状态变化监听
watch(isLoggedInFromState, (newValue) => {
  console.log(`isLoggedInFromState变化: ${newValue}`);
  isLoggedIn.value = newValue; // 更新本地ref
});

// 折叠/展开工具栏
const toggleExpand = () => {
  expanded.value = !expanded.value;
};

// 添加对登录状态变更的监听
const setupLoginStateListener = () => {
  // 监听登录状态变更
  const prevLoginState = isLoggedIn.value;

  // 创建一个侦听器，当登录状态变化时重置计时器
  watch(isLoggedIn, (newValue, oldValue) => {
    if (newValue !== oldValue) {
      // 登录状态已变更，重置计时器
      stateStartTime.value = new Date();
      stateDuration.value = 0;
      console.log("登录状态变更，重置状态计时器");
    }
  });

  // 监听全局状态变化事件
  globalStateManager.onStateChanged((event) => {
    // 每当全局状态变化，确保登录状态同步
    console.log("接收到全局状态变化，检查登录状态");

    // 注意这里不再使用直接赋值，而是让计算属性自动更新
    // 在下一个事件循环中检查一次同步状态
    setTimeout(() => {
      ensureLoginStateSync();
    }, 0);
  });
};

// 确保登录状态始终同步
const ensureLoginStateSync = () => {
  const currentLoginState = globalStateManager.isLoggedIn();

  // 直接更新本地登录状态ref
  if (currentLoginState !== isLoggedIn.value) {
    console.log(
      `强制同步登录状态: 全局=${currentLoginState}, 本地=${isLoggedIn.value}`
    );

    // 立即更新本地状态
    isLoggedIn.value = currentLoginState;

    // 触发一个状态变更事件，让其他组件也知道
    const currentState = globalStateManager.getState();
    if (currentState) {
      // 重设相同的状态以触发事件
      globalStateManager.setState(currentState.state, currentState.stateDesc);
    }

    // 通知父组件状态已变更
    setTimeout(() => {
      console.log("登录状态同步完成");
      emit("state-changed", agentState);
    }, 100);

    // 强制刷新当前组件
    setTimeout(() => {
      // 通过修改一个额外的标记来触发组件重新渲染
      forceUpdateCounter.value += 1;
    }, 10);
  }
};

// 创建一个辅助变量用于强制更新组件
const forceUpdateCounter = ref(0);

let skills = ref<any[]>([]);

// 添加ccbarInit方法，用于初始化话务条并调用接口
const ccbarInit = async () => {
  try {
    if (isInitialized.value) {
      return { state: true, msg: "话务条已初始化" };
    }

    addEvent("系统", "初始化话务条...");

    // 调用初始化接口
    const result = await ccbarService.init();

    if (result && result.state) {
      isInitialized.value = true;
      console.log("话务条初始化成功", result);
      addEvent("系统", "话务条初始化成功");
      skills.value = result.data.result.groups;
      // 触发初始化完成事件
      emit("init-complete", {
        success: true,
        data: result,
        skills: skills.value,
        timestamp: new Date().getTime(),
      });

      return {
        state: true,
        msg: "初始化成功",
        data: result,
      };
    } else {
      console.error("话务条初始化失败", result);
      addEvent("错误", `话务条初始化失败: ${result?.msg || "未知错误"}`);

      // 触发初始化完成事件(失败)
      emit("init-complete", {
        success: false,
        error: result?.msg || "初始化失败",
        timestamp: new Date().getTime(),
      });

      return {
        state: false,
        msg: result?.msg || "初始化失败",
        data: result,
      };
    }
  } catch (error: any) {
    console.error("话务条初始化出错", error);
    addEvent("错误", `话务条初始化出错: ${error.message || "未知错误"}`);

    // 触发初始化完成事件(错误)
    emit("init-complete", {
      success: false,
      error: error.message || "未知错误",
      timestamp: new Date().getTime(),
    });

    return {
      state: false,
      msg: error.message || "初始化出错",
      data: null,
    };
  }
};

// 组件挂载
onMounted(() => {
  console.log("CCBarToolbar组件已挂载, 配置:", props.config);

  // 确保配置有效并且包含baseURL
  if (!props.config || !props.config.baseURL) {
    console.warn("未提供有效的CCBar配置，请检查config属性是否正确传递");
    addEvent("错误", "未提供有效的CCBar配置，请检查config属性");
    return;
  }

  // 初始化服务（确保配置被传递）
  try {
    console.log("正在初始化CCBar服务，配置:", JSON.stringify(props.config));
    ccbarService = CCBarService.getInstance(props.config);

    // 初始化登录状态
    isLoggedIn.value = globalStateManager.isLoggedIn();

    // 启动状态计时器
    startStateTimer();

    addEvent("系统", "CCBar工具条已初始化");
  } catch (error) {
    console.error("初始化CCBar服务失败:", error);
    addEvent("错误", `初始化失败: ${error}`);
  }

  // 初始化事件监听
  setupEventHandlers();

  // 设置登录状态监听器
  setupLoginStateListener();

  // 启动状态持续时间计时器
  startStateTimer();

  // 从配置中获取外显号码
  if (props.config && props.config.outboundNumber) {
    outboundNumber.value = props.config.outboundNumber;
    console.log("从配置中获取外显号码:", outboundNumber.value);
  }

  // 确保初始登录状态同步
  ensureLoginStateSync();

  console.log("CCBar工具栏已挂载，初始登录状态:", isLoggedIn.value);

  // 取消注释，恢复原来的自动初始化逻辑
  ccbarInit();

  // 重置初始静音状态
  isMuted.value = false;

  // 增强静音状态监视
  watch(
    isMuted,
    (newValue) => {
      console.log("静音状态变化:", newValue);
      // 触发UI更新
      forceUpdateCounter.value += 1;

      // 在静音状态下，确保其他按钮状态保持一致
      if (newValue) {
        console.log("进入静音状态，检查按钮显示逻辑");
      } else {
        console.log("退出静音状态，检查按钮显示逻辑");
      }
    },
    { immediate: true }
  );

  // 添加状态变化监视
  watch(
    () => agentState.state,
    (newState) => {
      console.log("座席状态变化:", newState);
      // 当状态变为非TALK状态时，重置静音状态
      if (newState !== AgentStateType.TALK && isMuted.value) {
        console.log("非通话状态，重置静音状态");
        isMuted.value = false;
      }
      // 强制更新UI
      forceUpdateCounter.value += 1;
    }
  );

  // 在组件挂载后监听静音相关事件
  eventEmitter.on("call:muted", (data: any) => {
    console.log("检测到call:muted事件", data);
    isMuted.value = true;
    forceUpdateCounter.value += 1;
  });

  eventEmitter.on("call:unmuted", (data: any) => {
    console.log("检测到call:unmuted事件", data);
    isMuted.value = false;
    forceUpdateCounter.value += 1;
  });

  // 初始化时检查静音状态
  console.log("组件挂载时的静音状态:", isMuted.value);
});

// 组件卸载
onBeforeUnmount(() => {
  // 清除状态计时器
  if (stateTimer.value) {
    clearInterval(stateTimer.value);
    stateTimer.value = null;
  }
});

// 获取状态样式类
const getStateClass = () => {
  // 判断是否已登录，未登录时直接返回离线状态样式
  if (!isLoggedIn.value || agentState.state === AgentStateType.LOGOFF) {
    return "state-offline";
  }
  switch (agentState.state) {
    case AgentStateType.IDLE:
      return "state-ready";
    case AgentStateType.BUSY:
      return "state-busy";
    case AgentStateType.TALK:
      return "state-talking";
    case AgentStateType.ALERTING:
      return "state-ringing";
    case AgentStateType.HELD:
      return "state-held";
    case AgentStateType.WORKNOTREADY:
      return "state-acw";
    default:
      return "state-offline";
  }
};

// 添加事件日志
const addEvent = (type: string, content: string, id: string = "") => {
  const now = new Date();
  const time = `${now.getHours().toString().padStart(2, "0")}:${now
    .getMinutes()
    .toString()
    .padStart(2, "0")}:${now.getSeconds().toString().padStart(2, "0")}`;

  // 构建事件内容，如果提供了ID则添加
  const eventContent = id ? `[ID:${id}] ${content}` : content;
  latestEvent.value = { time, type, content: eventContent };

  // 向父组件发送事件通知
  emit("call-event", { time, type, content: eventContent });
};

// 设置事件处理器
const setupEventHandlers = () => {
  // 监听登录响应事件，获取外显号码
  eventManager.on("agent:loginResponse", (events: any, data: any) => {
    console.log("CCBarToolbar收到登录响应", events, data);

    // 首先确定哪个参数包含实际数据
    // EventEmitter会在第一个参数传递events对象，实际数据可能在第二个参数
    const responseData = data || (events && events.target ? events : null);

    // 安全检查数据
    if (!responseData) {
      console.error("登录响应数据为空", events, data);
      addEvent("登录", "登录响应数据无效", "LOGIN_INVALID_RESPONSE");

      return;
    }

    // 记录原始数据用于调试
    if (responseData.rawEvent) {
      console.log("登录响应原始事件:", responseData);
    }

    // 登录成功时显示通知
    if (responseData.resultCode === "0" || responseData.resultCode === 0) {
      // 添加详细日志
      console.log(
        `登录成功，工号: ${responseData.agentId}，时间: ${responseData.loginTime}`
      );

      // 设置外显号码
      if (
        responseData.outboundNumber ||
        responseData.rawEvent?.cmddata?.result?.outboundNumber
      ) {
        outboundNumber.value =
          responseData.outboundNumber ||
          responseData.rawEvent?.cmddata?.result?.outboundNumber ||
          "";
        console.log("设置外显号码:", outboundNumber.value);
      }

      // 确保登录状态更新，强制同步一次
      setTimeout(() => {
        isLoggedIn.value = true; // 立即更新本地状态

        // 登录后默认为BUSY状态（小休），而不是IDLE
        agentState.state = AgentStateType.BUSY as AgentStateType; // 修改为BUSY
        // 设置默认功能掩码
        setDefaultFuncMask(agentState.state, agentState.stateDesc);
        // 确保登出按钮可用
        agentState.funcMask.logoff = true;
        // 小休状态下只能置闲，不能置忙（已经是忙碌状态）
        agentState.funcMask.agentready = true; // 可以置闲
        agentState.funcMask.agentnotready = false; // 不能置忙
        forceUpdateCounter.value += 1; // 强制刷新
        console.log(
          "登录成功后更新状态:",
          isLoggedIn.value,
          "功能掩码:",
          agentState.funcMask
        );

        // 发送状态变更事件，确保父组件能收到
        emit("state-changed", {
          ...agentState,
          state: agentState.state,
          stateDesc: agentState.stateDesc,
        });

        // 登录成功后不再调用初始化方法，因为已经在组件挂载时自动初始化
        // onAfterLogin() 调用已被移除
      }, 100);
      localStorage.setItem("agentId", responseData.agentId);
      // 保存话机号码到localStorage
      if (responseData.phone) {
        localStorage.setItem("phone", responseData.phone);
      }
      // 添加通知消息
      addEvent(
        "登录",
        `登录成功，工号: ${responseData.agentId || "未知"}`,
        "LOGIN_SUCCESS"
      );

      // 发送登录成功事件
      emit("login-success", {
        agentId: responseData.agentId,
        phone: responseData.phone || "",
        state: agentState.state,
        stateDesc: agentState.stateDesc,
        funcMask: agentState.funcMask,
      });
    } else {
      // 登录失败通知
      console.error(
        `登录失败: ${responseData.resultDesc || "未知原因"}, 代码: ${
          responseData.resultCode || "未知"
        }`
      );

      addEvent(
        "登录",
        `登录失败: ${responseData.resultDesc || "未知原因"}`,
        "LOGIN_FAILED"
      );
    }
  });

  // 监听事件
  eventManager.on("agent:stateChanged", (events: any, data: any) => {
    // 首先确定哪个参数包含实际数据
    const responseData = data || (events && events.target ? events : null);
    console.log("收到座席状态变更事件:", responseData);

    // 安全检查数据
    if (!responseData) {
      addEvent("状态", "座席状态数据无效", "STATE_INVALID_DATA");
      return;
    }

    // 从cmddata获取状态信息
    const stateData = responseData.cmddata || responseData;

    if (stateData) {
      // 检查状态是否发生变化
      if (stateData.state && stateData.state !== agentState.state) {
        // 状态已变更，重置计时器
        stateStartTime.value = new Date();
        stateDuration.value = 0;
      }

      // 更新状态
      if (stateData.state) {
        agentState.state = stateData.state as AgentStateType;
        agentState.stateDesc =
          stateData.stateDesc || getStateDescription(stateData.state);
        addEvent(
          "状态变更",
          `座席状态: ${agentState.stateDesc} (${stateData.state})`,
          "STATE_CHANGED"
        );

        // 如果状态变为HELD（通话保持），确保恢复和挂断按钮可用
        if (agentState.state === AgentStateType.HELD) {
          console.log("检测到HELD状态，确保恢复和挂断按钮可用");
          agentState.funcMask.unholdcall = true;
          agentState.funcMask.clearcall = true;
        }

        // 如果状态变为TALK（通话中），确保有通话数据
        if (
          agentState.state === AgentStateType.TALK &&
          !currentCallData.value
        ) {
          console.log("检测到TALK状态，但没有通话数据，创建空的通话数据");
          currentCallData.value = {
            callId: "",
            caller: "114",
            called: "",
            agentId: agentId.value || localStorage.getItem("agentId") || "",
          };
        }
      }

      // 更新功能掩码
      if (stateData.funcMask) {
        agentState.funcMask = { ...stateData.funcMask };

        // 无论服务器返回什么，确保HELD状态下恢复和挂断按钮可用
        if (agentState.state === AgentStateType.HELD) {
          agentState.funcMask.unholdcall = true;
          agentState.funcMask.clearcall = true;
        }

        // 确保TALK状态下挂断按钮可用
        if (agentState.state === AgentStateType.TALK) {
          agentState.funcMask.clearcall = true;
        }
      } else {
        // 如果没有功能掩码，根据状态设置默认值
        setDefaultFuncMask(agentState.state, agentState.stateDesc);
      }

      // 确保登出按钮始终可用（在已登录状态）
      if (isLoggedIn.value) {
        agentState.funcMask.logoff = true;
      }

      // 向父组件发送状态变更事件
      emit("state-changed", agentState);
    }
  });

  // 监听agentStateSync事件
  eventManager.on("agentStateSync", (events: any, data: any) => {
    // 首先确定哪个参数包含实际数据
    const responseData = data || (events && events.target ? events : null);
    console.log("收到座席状态同步事件:", responseData);

    // 安全检查数据
    if (!responseData) {
      console.error("座席状态同步数据为空", events, data);
      addEvent("状态", "座席状态同步数据无效", "STATE_SYNC_INVALID");
      return;
    }

    // 从cmddata获取状态信息
    const stateData = responseData.cmddata || responseData;

    if (stateData) {
      // 检查状态是否发生变化
      if (stateData.state && stateData.state !== agentState.state) {
        // 状态已变更，重置计时器
        stateStartTime.value = new Date();
        stateDuration.value = 0;
        console.log("座席状态已同步，重置计时器:", stateData.state);
      }

      // 更新状态
      if (stateData.state) {
        agentState.state = stateData.state as AgentStateType;
        agentState.stateDesc =
          stateData.stateDesc || getStateDescription(stateData.state);
        addEvent(
          "状态同步",
          `座席状态更新: ${agentState.stateDesc} (${stateData.state})`,
          "STATE_SYNC_UPDATED"
        );
      }

      // 更新功能掩码
      if (stateData.funcMask) {
        agentState.funcMask = { ...stateData.funcMask };
      } else {
        // 如果没有功能掩码，根据状态设置默认值
        setDefaultFuncMask(agentState.state, agentState.stateDesc);
      }

      // 确保登出按钮始终可用（在已登录状态）
      if (isLoggedIn.value) {
        agentState.funcMask.logoff = true;
      }

      // 根据不同状态确保相应按钮可用
      if (agentState.state === AgentStateType.HELD) {
        // HELD状态下确保可以挂断和恢复
        console.log("状态同步检测到HELD状态，确保恢复和挂断按钮可用");
        agentState.funcMask.unholdcall = true;
        agentState.funcMask.clearcall = true;
      } else if (
        agentState.state === AgentStateType.IDLE ||
        (agentState.state === AgentStateType.BUSY &&
          agentState.stateDesc === "小休") ||
        agentState.stateDesc === "空闲"
      ) {
        // 修改为：小休状态下只能置闲，不能置忙
        if (
          agentState.state === AgentStateType.BUSY &&
          agentState.stateDesc === "小休"
        ) {
          agentState.funcMask.agentready = true; // 可以置闲
          agentState.funcMask.agentnotready = false; // 不能置忙（已经是忙碌状态）
        } else if (
          agentState.state === AgentStateType.IDLE &&
          agentState.stateDesc === "空闲"
        ) {
          // 空闲状态可以置忙
          agentState.funcMask.agentready = true;
          agentState.funcMask.agentnotready = true;
        }
      }

      // 向父组件发送状态变更事件
      emit("state-changed", agentState);
    }
  });
  // 监听呼叫事件
  eventManager.on("call:eventSync", (events: any, data: any) => {
    const responseData = data || (events && events.target ? events : null);
    console.log("收到呼叫事件:", responseData);

    // 安全检查数据
    if (!responseData) {
      console.error("呼叫事件数据为空", events, data);
      addEvent("呼叫", "呼叫事件数据无效");
      return;
    }
    const { agentId, callId, callEventId, entId, caller, called, custPhone } =
      responseData;

    // 保存通话数据，以便后续操作使用
    currentCallData.value = responseData;
    console.log("已保存当前通话数据:", currentCallData.value);
    if (callEventId === "evtAltering") {
      ElMessage({
        message: `振铃中：${custPhone}`,
        type: "info",
        plain: true,
      });
    } else if (callEventId === "evtConnected") {
      ElMessage({
        message: `正在通话`,
        type: "info",
        plain: true,
      });
    }
    // 保存通话数据
    currentCallData.value = responseData;
    addEvent(
      "呼叫",
      `呼叫事件: ${callId}, 事件ID: ${callEventId}, 企业ID: ${entId}, 主叫: ${caller}, 被叫: ${called}`
    );
  });

  // 监听polling:stateSync事件，确保状态同步
  eventManager.on("polling:stateSync", (events: any, data: any) => {
    // 首先确定哪个参数包含实际数据
    const responseData = data || (events && events.target ? events : null);
    console.log("收到轮询状态同步事件:", responseData);

    // 安全检查数据
    if (!responseData) {
      console.error("轮询状态同步数据为空", events, data);
      return;
    }

    // 确保登录状态始终同步
    ensureLoginStateSync();

    // 检查座席状态是否需要更新
    const globalState = globalStateManager.getState();
    if (globalState) {
      // globalState本身就是AgentState类型，直接使用
      if (globalState.state && globalState.state !== agentState.state) {
        console.log(
          `检测到座席状态不一致，全局状态: ${globalState.state}, UI状态: ${agentState.state}`
        );

        // 更新座席状态 - 使用类型断言避免类型错误
        agentState.state = globalState.state as AgentStateType;
        agentState.stateDesc =
          globalState.stateDesc || getStateDescription(globalState.state);

        // 检查并更新功能掩码
        if (globalState.funcMask) {
          agentState.funcMask = { ...globalState.funcMask };
        } else {
          // 如果没有功能掩码，根据状态设置默认值
          setDefaultFuncMask(agentState.state, agentState.stateDesc);
        }

        // 确保登出按钮始终可用（在已登录状态）
        if (isLoggedIn.value) {
          agentState.funcMask.logoff = true;
        }

        // 特殊处理HELD状态下的按钮控制
        if (agentState.state === AgentStateType.HELD) {
          console.log("在轮询同步中检测到HELD状态，确保恢复和挂断按钮可用");
          agentState.funcMask.unholdcall = true;
          agentState.funcMask.clearcall = true;
        }

        // 特殊处理TALK状态下的按钮控制和通话数据
        if (agentState.state === AgentStateType.TALK) {
          console.log("在轮询同步中检测到TALK状态，确保挂断按钮可用");
          agentState.funcMask.clearcall = true;

          // 如果没有通话数据，创建一个空的
          if (!currentCallData.value) {
            console.log("TALK状态但没有通话数据，创建默认通话数据");
            currentCallData.value = {
              callId: "",
              caller: "114",
              called: "",
              agentId: agentId.value || localStorage.getItem("agentId") || "",
            };
          }
        }

        // 确保小休状态下可以置忙置闲
        if (
          agentState.state === AgentStateType.IDLE ||
          (agentState.state === AgentStateType.BUSY &&
            agentState.stateDesc === "小休") ||
          agentState.stateDesc === "空闲"
        ) {
          // 修改为：小休状态下只能置闲，不能置忙
          if (
            agentState.state === AgentStateType.BUSY &&
            agentState.stateDesc === "小休"
          ) {
            agentState.funcMask.agentready = true; // 可以置闲
            agentState.funcMask.agentnotready = false; // 不能置忙（已经是忙碌状态）
          } else if (
            agentState.state === AgentStateType.IDLE &&
            agentState.stateDesc === "空闲"
          ) {
            // 空闲状态可以置忙
            agentState.funcMask.agentready = true;
            agentState.funcMask.agentnotready = true;
          }
        }

        // 重置计时器
        stateStartTime.value = new Date();
        stateDuration.value = 0;

        addEvent("状态同步", `座席状态已同步: ${agentState.stateDesc}`);
      } else {
        // 即使状态相同，也确保功能掩码正确
        // 确保登出按钮始终可用（在已登录状态）
        if (isLoggedIn.value) {
          agentState.funcMask.logoff = true;
        }

        // 特殊检查HELD状态，确保按钮可用
        if (agentState.state === AgentStateType.HELD) {
          console.log("在轮询同步中检查HELD状态按钮可用性");
          if (!agentState.funcMask.unholdcall) {
            console.log("修复HELD状态下恢复按钮不可用的问题");
            agentState.funcMask.unholdcall = true;
          }
          if (!agentState.funcMask.clearcall) {
            console.log("修复HELD状态下挂断按钮不可用的问题");
            agentState.funcMask.clearcall = true;
          }
        }

        // 特殊检查TALK状态，确保挂断按钮可用
        if (agentState.state === AgentStateType.TALK) {
          console.log("在轮询同步中检查TALK状态挂断按钮可用性");
          if (!agentState.funcMask.clearcall) {
            console.log("修复TALK状态下挂断按钮不可用的问题");
            agentState.funcMask.clearcall = true;
          }

          // 确保TALK状态有通话数据
          if (!currentCallData.value) {
            console.log("TALK状态但无通话数据，创建默认通话数据");
            currentCallData.value = {
              callId: "",
              caller: "114",
              called: "",
              agentId: agentId.value || localStorage.getItem("agentId") || "",
            };
          }
        }

        // 确保小休状态下可以置忙置闲
        if (
          agentState.state === AgentStateType.IDLE ||
          (agentState.state === AgentStateType.BUSY &&
            agentState.stateDesc === "小休") ||
          agentState.stateDesc === "空闲"
        ) {
          // 修改为：小休状态下只能置闲，不能置忙
          if (
            agentState.state === AgentStateType.BUSY &&
            agentState.stateDesc === "小休"
          ) {
            agentState.funcMask.agentready = true; // 可以置闲
            agentState.funcMask.agentnotready = false; // 不能置忙（已经是忙碌状态）
          } else if (
            agentState.state === AgentStateType.IDLE &&
            agentState.stateDesc === "空闲"
          ) {
            // 空闲状态可以置忙
            agentState.funcMask.agentready = true;
            agentState.funcMask.agentnotready = true;
          }
        }
      }
    }
  });

  // 监听静音事件
  eventManager.on("call:muted", (events: any, data: any) => {
    const responseData = data || (events && events.target ? events : null);
    console.log("收到静音事件:", responseData);

    // 更新静音状态
    isMuted.value = true;
    console.log("设置isMuted=true，麦克风已静音");
    addEvent("呼叫", "麦克风已静音");
    // 强制组件更新
    forceUpdateCounter.value += 1;
  });

  // 监听取消静音事件
  eventManager.on("call:unmuted", (events: any, data: any) => {
    const responseData = data || (events && events.target ? events : null);
    console.log("收到取消静音事件:", responseData);

    // 更新静音状态
    isMuted.value = false;
    console.log("设置isMuted=false，麦克风已取消静音");
    addEvent("呼叫", "麦克风已取消静音");
    // 强制组件更新
    forceUpdateCounter.value += 1;
  });

  // 监听403错误事件
  eventManager.on("session:403error", (data: any) => {
    console.log("收到403错误事件:", data);

    // 检查当前是否已登录，避免重复处理
    if (isLoggedIn.value) {
      addEvent("系统", "检测到会话已过期，正在重置状态", "SESSION_EXPIRED");

      try {
        // 使用CCBarService的resetLoginState方法重置登录状态，不调用签出接口
        ccbarService.resetLoginState();

        // 强制更新本地状态
        isLoggedIn.value = false;
        sessionStorage.setItem("isLogined", "false"); // 确保sessionStorage更新

        agentState.state = AgentStateType.LOGOFF;
        agentState.stateDesc = "登出";
        agentState.funcMask = {
          logon: true, // 仅允许登录操作
          logoff: false,
          makecall: false,
          clearcall: false,
          holdcall: false,
          unholdcall: false,
          transfercall: false,
          conference: false,
          agentready: false,
          agentnotready: false,
        };

        // 清理外显号码
        outboundNumber.value = "";

        addEvent("系统", "状态已重置为登出状态", "STATE_RESET");

        // 强制刷新界面
        forceUpdateCounter.value += 1;
      } catch (error) {
        console.error("重置状态失败:", error);
        addEvent("错误", `重置状态失败: ${error}`, "RESET_ERROR");
      }
    } else {
      console.log("当前未登录，忽略403错误事件");
    }
  });

  // 监听轮询停止事件
  eventManager.on("polling:stopped", (data: any) => {
    console.log("收到轮询已停止事件:", data);
    addEvent("系统", "轮询服务已停止", "POLLING_STOPPED");
  });

  // 监听TALK状态事件
  eventManager.on("agent:talk", (events: any, data: any) => {
    console.log("收到通话状态事件:", data);
    // 确保在通话状态下，静音状态正确初始化
    isMuted.value = false;
    // 强制刷新UI
    forceUpdateCounter.value += 1;
  });

  // 监听HELD状态事件
  eventManager.on("agent:held", (events: any, data: any) => {
    console.log("收到保持状态事件:", data);
    // 确保在保持状态下，静音状态重置
    isMuted.value = false;
    // 强制刷新UI
    forceUpdateCounter.value += 1;
  });
};

// 获取状态描述
const getStateDescription = (state: string): string => {
  const stateMap: Record<string, string> = {
    IDLE: "空闲",
    READY: "就绪",
    BUSY: "忙碌",
    TALK: "通话中",
    ALERTING: "振铃中",
    HELD: "保持中",
    OFFLINE: "离线",
    ACW: "话后处理",
  };

  return stateMap[state] || state;
};

// 座席置闲
const setAgentReady = async () => {
  if (!canSetReady.value) {
    addEvent("错误", "当前状态不允许设置为空闲", "SET_READY_NOT_ALLOWED");
    return;
  }

  try {
    const result = await ccbarService.agentReady();

    if (result.state) {
      addEvent("状态", "座席已置闲", "SET_READY_SUCCESS");
    } else {
      addEvent(
        "错误",
        `置闲失败: ${result.msg || "未知错误"}`,
        "SET_READY_FAILED"
      );
    }
  } catch (error: any) {
    addEvent(
      "错误",
      `置闲出错: ${error.message || "未知错误"}`,
      "SET_READY_ERROR"
    );
  }
};

// 座席置忙
const setAgentNotReady = async () => {
  if (!canSetNotReady.value) {
    addEvent("错误", "当前状态不允许设置为繁忙", "SET_NOTREADY_NOT_ALLOWED");
    return;
  }

  try {
    const result = await ccbarService.agentNotReady();
    console.log(result, 123456);
    if (result.state) {
      addEvent("状态", "座席已置忙", "SET_NOTREADY_SUCCESS");
    } else {
      addEvent(
        "错误",
        `置忙失败: ${result.msg || "未知错误"}`,
        "SET_NOTREADY_FAILED"
      );
    }
  } catch (error: any) {
    addEvent(
      "错误",
      `置忙出错: ${error.message || "未知错误"}`,
      "SET_NOTREADY_ERROR"
    );
  }
};

// 设置特定状态
const setAgentState = async (state: string) => {
  // 根据不同原因码设置座席状态
  let reasonCode;
  let reasonDesc;

  switch (state) {
    case "break":
      reasonCode = "1";
      reasonDesc = "休息中";
      break;
    case "meeting":
      reasonCode = "2";
      reasonDesc = "会议中";
      break;
    case "training":
      reasonCode = "3";
      reasonDesc = "培训中";
      break;
    default:
      reasonCode = "0";
      reasonDesc = "其他原因";
  }

  try {
    const result = await ccbarService.agentNotReady(reasonCode);
    console.log("设置状态变更结果:", result);
    if (result.state) {
      addEvent(
        "状态变更",
        `座席状态变更为: ${reasonDesc}`,
        `SET_STATE_${state.toUpperCase()}`
      );
    } else {
      addEvent(
        "错误",
        `状态变更失败: ${result.msg || "未知错误"}`,
        `SET_STATE_${state.toUpperCase()}_FAILED`
      );
    }
  } catch (error: any) {
    addEvent(
      "错误",
      `状态变更出错: ${error.message || "未知错误"}`,
      `SET_STATE_${state.toUpperCase()}_ERROR`
    );
  }
};

// 拨打电话
const makeCall = async () => {
  if (!phoneNumber.value) {
    addEvent("警告", "请输入电话号码", "MAKE_CALL_NO_NUMBER");
    return;
  }

  if (!canMakeCall.value) {
    addEvent("错误", "当前状态不允许拨打电话", "MAKE_CALL_NOT_ALLOWED");
    return;
  }

  try {
    addEvent("呼叫", `开始拨号: ${phoneNumber.value}`, "MAKE_CALL_START");
    const result = await ccbarService.makeCall(phoneNumber.value);
    ElMessage({
      message: `正在呼叫: ${phoneNumber.value}`,
      type: "info",
      plain: true,
    });
    if (result.state) {
      addEvent("呼叫", `正在呼叫: ${phoneNumber.value}`, "MAKE_CALL_DIALING");
    } else {
      addEvent(
        "错误",
        `拨号失败: ${result.msg || "未知错误"}`,
        "MAKE_CALL_FAILED"
      );
    }
  } catch (error: any) {
    addEvent(
      "错误",
      `拨号失败: ${error.message || "未知错误"}`,
      "MAKE_CALL_ERROR"
    );
  }
};

// 挂断电话
const hangupCall = async () => {
  if (!canHangupCall.value) {
    addEvent("错误", "当前状态不允许挂断电话", "HANGUP_CALL_NOT_ALLOWED");
    return;
  }
  try {
    addEvent("呼叫", "挂断电话", "HANGUP_CALL_START");
    // 如果有保存的通话数据，使用其中的callId等信息
    const callDataToUse = currentCallData.value || {
      // 创建一个默认的通话数据对象
      callId: "", // 空的callId
      caller: "114", // 默认主叫
      called: "", // 默认被叫
      agentId: agentId.value || localStorage.getItem("agentId") || "",
    };

    console.log("使用以下数据进行挂断:", callDataToUse);
    const result = await ccbarService.clearCall(callDataToUse);

    if (result.state) {
      addEvent("呼叫", "通话已结束", "HANGUP_CALL_SUCCESS");
      // 清除保存的通话数据
      currentCallData.value = null;
      console.log("手动挂断通话，清除通话数据");
    } else {
      addEvent(
        "错误",
        `挂断失败: ${result.msg || "未知错误"}`,
        "HANGUP_CALL_FAILED"
      );
    }
  } catch (error: any) {
    addEvent(
      "错误",
      `挂断失败: ${error.message || "未知错误"}`,
      "HANGUP_CALL_ERROR"
    );
  }
};

// 保持通话
const holdCall = async () => {
  if (!canHoldCall.value) {
    addEvent("错误", "当前状态不允许保持电话", "HOLD_CALL_NOT_ALLOWED");
    return;
  }

  try {
    addEvent("呼叫", "保持通话", "HOLD_CALL_START");
    // 如果有保存的通话数据，使用其中的信息
    if (currentCallData.value) {
      console.log("使用保存的通话数据进行保持:", currentCallData.value);
      // 这里可以根据需要传递通话数据给holdCall方法
    }
    const result = await ccbarService.holdCall();
    if (result.state) {
      addEvent("呼叫", "通话已保持", "HOLD_CALL_SUCCESS");
    } else {
      addEvent(
        "错误",
        `保持失败: ${result.msg || "未知错误"}`,
        "HOLD_CALL_FAILED"
      );
    }
  } catch (error: any) {
    addEvent(
      "错误",
      `保持失败: ${error.message || "未知错误"}`,
      "HOLD_CALL_ERROR"
    );
  }
};

// 恢复通话
const retrieveCall = async () => {
  console.log(
    "尝试恢复通话，当前状态:",
    agentState.state,
    "恢复按钮可用:",
    canRetrieveCall.value
  );
  console.log("功能掩码:", JSON.stringify(agentState.funcMask));

  if (!canRetrieveCall.value) {
    addEvent("错误", "当前状态不允许恢复电话", "RETRIEVE_CALL_NOT_ALLOWED");
    return;
  }

  try {
    addEvent("呼叫", "恢复通话", "RETRIEVE_CALL_START");
    // 如果有保存的通话数据，使用其中的信息
    if (currentCallData.value) {
      console.log("使用保存的通话数据进行恢复:", currentCallData.value);
      // 这里可以根据需要传递通话数据给unholdCall方法
    }
    const result = await ccbarService.unholdCall();

    if (result.state) {
      addEvent("呼叫", "通话已恢复", "RETRIEVE_CALL_SUCCESS");
    } else {
      addEvent(
        "错误",
        `恢复失败: ${result.msg || "未知错误"}`,
        "RETRIEVE_CALL_FAILED"
      );
    }
  } catch (error: any) {
    addEvent(
      "错误",
      `恢复失败: ${error.message || "未知错误"}`,
      "RETRIEVE_CALL_ERROR"
    );
  }
};

// 静音通话
const muteCall = async () => {
  console.log("尝试静音通话，当前静音状态:", isMuted.value);
  if (!canMuteCall.value) {
    console.log("当前状态不允许静音，canMuteCall:", canMuteCall.value);
    addEvent("错误", "当前状态不允许静音", "MUTE_CALL_NOT_ALLOWED");
    return;
  }

  try {
    addEvent("呼叫", "静音通话", "MUTE_CALL_START");
    const result = await ccbarService.muteCall();
    console.log("静音通话请求结果:", result);

    if (result.state) {
      // 直接设置静音状态为true
      isMuted.value = true;
      console.log("静音成功，设置isMuted=true");
      addEvent("呼叫", "麦克风已静音", "MUTE_CALL_SUCCESS");

      // 手动触发事件以确保状态一致
      eventEmitter.emit("call:muted", { timestamp: new Date().getTime() });

      // 强制更新视图
      forceUpdateCounter.value += 1;

      // 额外验证
      setTimeout(() => {
        if (!isMuted.value) {
          console.warn("静音状态未正确保持，再次强制设置");
          isMuted.value = true;
          forceUpdateCounter.value += 1;
        }
      }, 100);
    } else {
      addEvent(
        "错误",
        `静音失败: ${result.msg || "未知错误"}`,
        "MUTE_CALL_FAILED"
      );
    }
  } catch (error: any) {
    console.error("静音操作异常:", error);
    addEvent(
      "错误",
      `静音失败: ${error.message || "未知错误"}`,
      "MUTE_CALL_ERROR"
    );
  }
};

// 取消静音通话
const unmuteCall = async () => {
  console.log("尝试取消静音通话，当前静音状态:", isMuted.value);
  if (!canUnmuteCall.value) {
    console.log("当前状态不允许取消静音，canUnmuteCall:", canUnmuteCall.value);
    addEvent("错误", "当前状态不允许取消静音", "UNMUTE_CALL_NOT_ALLOWED");
    return;
  }

  try {
    addEvent("呼叫", "取消静音通话", "UNMUTE_CALL_START");
    const result = await ccbarService.unmuteCall();
    console.log("取消静音通话请求结果:", result);

    if (result.state) {
      // 直接设置静音状态为false
      isMuted.value = false;
      console.log("取消静音成功，设置isMuted=false");
      addEvent("呼叫", "麦克风已取消静音", "UNMUTE_CALL_SUCCESS");

      // 手动触发事件以确保状态一致
      eventEmitter.emit("call:unmuted", { timestamp: new Date().getTime() });

      // 强制更新视图
      forceUpdateCounter.value += 1;

      // 额外验证
      setTimeout(() => {
        if (isMuted.value) {
          console.warn("非静音状态未正确保持，再次强制设置");
          isMuted.value = false;
          forceUpdateCounter.value += 1;
        }
      }, 100);
    } else {
      addEvent(
        "错误",
        `取消静音失败: ${result.msg || "未知错误"}`,
        "UNMUTE_CALL_FAILED"
      );
    }
  } catch (error: any) {
    console.error("取消静音操作异常:", error);
    addEvent(
      "错误",
      `取消静音失败: ${error.message || "未知错误"}`,
      "UNMUTE_CALL_ERROR"
    );
  }
};

// 添加一个手动测试静音状态的方法，这对调试非常有用
const testMuteToggle = async () => {
  console.log("测试静音状态切换");

  if (isMuted.value) {
    // 当前是静音状态，模拟取消静音
    console.log("当前是静音状态，尝试取消静音");
    isMuted.value = false;
    eventEmitter.emit("call:unmuted", { timestamp: new Date().getTime() });
  } else {
    // 当前是非静音状态，模拟静音
    console.log("当前是非静音状态，尝试静音");
    isMuted.value = true;
    eventEmitter.emit("call:muted", { timestamp: new Date().getTime() });
  }

  // 强制更新视图
  forceUpdateCounter.value += 1;

  addEvent(
    "测试",
    `测试切换静音状态: ${isMuted.value ? "已静音" : "已取消静音"}`
  );

  return {
    newState: isMuted.value,
    message: `静音状态已切换为: ${isMuted.value ? "已静音" : "已取消静音"}`,
  };
};

// 登出按钮点击事件处理器
const onLogoutButtonClick = () => {
  handleLogout(false);
};

// 登出
const handleLogout = async (isAutoLogout: boolean = false) => {
  // 如果不是自动登出，需要检查状态
  if (!isAutoLogout && !canLogout.value) {
    addEvent("错误", "当前状态不允许登出");
    return;
  }

  try {
    // 如果是自动登出，记录日志
    if (isAutoLogout) {
      console.log("执行自动签出操作");
    }

    const result = await ccbarService.logout();

    if (result.state) {
      // 登出成功，重置计时器
      stateStartTime.value = new Date();
      stateDuration.value = 0;

      // 立即更新登录状态
      isLoggedIn.value = false;

      // 重置座席状态为未登录状态
      agentState.state = AgentStateType.LOGOFF;
      agentState.stateDesc = "登出";
      // 清空功能掩码
      agentState.funcMask = {
        logon: true, // 仅允许登录操作
        logoff: false,
        makecall: false,
        clearcall: false,
        holdcall: false,
        unholdcall: false,
        transfercall: false,
        conference: false,
        agentready: false,
        agentnotready: false,
      };

      // 清空外显号码
      outboundNumber.value = "";

      // 更新全局状态管理器的状态
      globalStateManager.resetState(); // 重置全局状态

      forceUpdateCounter.value += 1; // 强制刷新
      console.log(
        "登出成功后更新状态:",
        isLoggedIn.value,
        "座席状态:",
        agentState.state
      );

      // 区分自动登出和手动登出的消息
      if (isAutoLogout) {
        addEvent("系统", "会话已过期，系统自动签出");
      } else {
        addEvent("登出", "签出成功");
      }

      emit("logout-success");
    } else {
      addEvent("错误", `签出失败: ${result.msg || "未知错误"}`);
    }
  } catch (error: any) {
    addEvent("错误", `签出失败: ${error.message || "未知错误"}`);
  }
};

// 启动状态持续时间计时器
const startStateTimer = () => {
  // 先清除可能存在的计时器
  if (stateTimer.value) {
    clearInterval(stateTimer.value);
  }

  // 初始化计时器，每秒更新一次
  stateTimer.value = window.setInterval(() => {
    const now = new Date();
    stateDuration.value = Math.floor(
      (now.getTime() - stateStartTime.value.getTime()) / 1000
    );
  }, 1000);
};

// 计算状态持续时间文本
const stateDurationText = computed(() => {
  const hours = Math.floor(stateDuration.value / 3600);
  const minutes = Math.floor((stateDuration.value % 3600) / 60);
  const seconds = stateDuration.value % 60;

  if (hours > 0) {
    return `${hours}小时${minutes}分${seconds}秒`;
  } else if (minutes > 0) {
    return `${minutes}分${seconds}秒`;
  } else {
    return `${seconds}秒`;
  }
});

// 添加一个格式化通话时长的辅助函数
const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  if (hours > 0) {
    return `${hours}小时${minutes}分${remainingSeconds}秒`;
  } else if (minutes > 0) {
    return `${minutes}分${remainingSeconds}秒`;
  } else {
    return `${remainingSeconds}秒`;
  }
};

// 设置默认功能掩码
const setDefaultFuncMask = (state: AgentStateType, stateDesc?: string) => {
  // 默认所有操作不可用
  const defaultMask = {
    logon: false,
    logoff: false,
    makecall: false,
    clearcall: false,
    holdcall: false,
    unholdcall: false,
    transfercall: false,
    conference: false,
    agentready: false,
    agentnotready: false,
  };

  // 根据状态设置默认功能掩码
  switch (state) {
    case AgentStateType.LOGOFF:
      // 未登录状态
      defaultMask.logon = true;
      break;
    case AgentStateType.IDLE:
      // 空闲状态
      defaultMask.logoff = true;
      defaultMask.makecall = true;
      // 空闲状态 - 可以置忙
      defaultMask.agentnotready = true;
      break;
    case AgentStateType.BUSY:
      // 忙碌状态
      defaultMask.logoff = true;
      defaultMask.agentready = true; // 可以置闲
      defaultMask.makecall = true; // 允许拨打电话
      break;
    case AgentStateType.TALK:
      // 通话中
      defaultMask.clearcall = true; // 确保挂断按钮始终可用
      defaultMask.holdcall = true;
      defaultMask.transfercall = true;
      defaultMask.logoff = true; // 通话中也可以登出
      break;
    case AgentStateType.HELD:
      // 保持中 - 强制设置恢复和挂断按钮可用
      defaultMask.unholdcall = true;
      defaultMask.clearcall = true; // 确保在保持状态下可以挂断
      defaultMask.logoff = true; // 保持中也可以登出
      console.log("在保持状态下设置默认功能掩码: 恢复和挂断按钮已启用");
      break;
    default:
      // 其他状态下，允许登出
      defaultMask.logoff = true;
  }

  // 设置功能掩码
  agentState.funcMask = { ...defaultMask };
  console.log("已设置默认功能掩码:", state, stateDesc, agentState.funcMask);
};

// 获取当前通话数据
const getCurrentCallData = () => {
  return currentCallData.value;
};

// 添加一个手动测试方法，确保isMuted状态可以正确切换并影响界面
// 在getCurrentCallData方法之后添加
// ... existing code ...
// 手动切换静音状态（仅用于测试）
const toggleMuteState = () => {
  isMuted.value = !isMuted.value;
  console.log("手动切换静音状态:", isMuted.value);

  // 测试直接触发相应事件
  if (isMuted.value) {
    eventEmitter.emit("call:muted", { timestamp: new Date().getTime() });
    addEvent("测试", "手动切换为静音状态");
  } else {
    eventEmitter.emit("call:unmuted", { timestamp: new Date().getTime() });
    addEvent("测试", "手动切换为非静音状态");
  }

  // 强制刷新组件
  forceUpdateCounter.value += 1;
};

// 向外部暴露测试方法
defineExpose({
  setAgentReady,
  setAgentNotReady,
  setAgentState,
  makeCall,
  hangupCall,
  holdCall,
  retrieveCall,
  muteCall,
  unmuteCall,
  handleLogout,
  ccbarInit,
  getCurrentCallData,
  toggleMuteState, // 新增的测试方法
  testMuteToggle, // 新增测试方法
});

// 添加HELD状态按钮可用性监视
watch(
  () => agentState.state,
  (newState, oldState) => {
    if (newState === AgentStateType.HELD) {
      console.log(
        "检测到状态变为HELD，当前功能掩码:",
        JSON.stringify(agentState.funcMask)
      );
      // 确保恢复和挂断按钮在HELD状态下始终可用
      setTimeout(() => {
        if (!agentState.funcMask.unholdcall) {
          console.log("修复HELD状态下恢复按钮不可用问题");
          agentState.funcMask.unholdcall = true;
        }
        if (!agentState.funcMask.clearcall) {
          console.log("修复HELD状态下挂断按钮不可用问题");
          agentState.funcMask.clearcall = true;
        }
      }, 100);
    }
  },
  { immediate: true }
);

// 单独监视canRetrieveCall计算属性
watch(
  () => canRetrieveCall.value,
  (isEnabled) => {
    if (agentState.state === AgentStateType.HELD && !isEnabled) {
      console.warn("HELD状态下恢复按钮被禁用，强制启用");
      // 强制更新计算属性依赖值
      agentState.funcMask.unholdcall = true;
    }
  }
);

// 添加组合状态监视，用于调试
watch([() => agentState.state, () => isMuted.value], ([state, muted]) => {
  console.log(`当前状态组合: 状态=${state}, 静音=${muted}`);
  console.log("可用按钮情况:", {
    canHangup: canHangupCall.value,
    canHold: canHoldCall.value,
    canRetrieve: canRetrieveCall.value,
    canMute: canMuteCall.value,
    canUnmute: canUnmuteCall.value,
  });
});

// 添加调试代码以确保按钮显示逻辑正确
// 这将记录当前静音状态和各按钮的显示条件
watch(
  [() => isMuted.value, () => agentState.state],
  () => {
    console.log("=== 按钮状态调试信息 ===");
    console.log(`当前静音状态: ${isMuted.value ? "已静音" : "未静音"}`);
    console.log(`当前代理状态: ${agentState.state}`);
    console.log(`挂断按钮可用: ${canHangupCall.value}`);
    console.log(`保持按钮可用: ${canHoldCall.value}`);
    console.log(`恢复按钮可用: ${canRetrieveCall.value}`);
    console.log(`静音按钮可用: ${canMuteCall.value}`);
    console.log(`取消静音按钮可用: ${canUnmuteCall.value}`);
    console.log(
      `静音按钮显示: ${
        !isMuted.value && agentState.state !== AgentStateType.HELD
      }`
    );
    console.log(`取消静音按钮显示: ${isMuted.value}`);
    console.log("========================");
  },
  { immediate: true }
);

// ... existing code ...
// 添加一个更全面的按钮状态调试函数
const logButtonStates = () => {
  console.log("=== 按钮状态调试信息 ===");
  console.log(`当前静音状态: ${isMuted.value ? "已静音" : "未静音"}`);
  console.log(`当前代理状态: ${agentState.state}`);
  console.log(`挂断按钮可用: ${canHangupCall.value}`);
  console.log(`保持按钮可用: ${canHoldCall.value}`);
  console.log(`恢复按钮可用: ${canRetrieveCall.value}`);
  console.log(`静音按钮可用: ${canMuteCall.value}`);
  console.log(`取消静音按钮可用: ${canUnmuteCall.value}`);
  console.log(
    `静音按钮显示: ${
      !isMuted.value && agentState.state !== AgentStateType.HELD
    }`
  );
  console.log(`取消静音按钮显示: ${isMuted.value}`);
  console.log("========================");
};

// 确保在关键事件和状态变化时调用此函数
watch(
  [() => isMuted.value, () => agentState.state],
  () => {
    logButtonStates();
  },
  { immediate: true }
);

// 添加对代理状态的监听，确保在状态变化时重置静音状态
watch(
  () => agentState.state,
  (newState, oldState) => {
    console.log(`代理状态变更: ${oldState} -> ${newState}`);

    // 如果状态从TALK变为其他状态，重置静音状态
    if (oldState === AgentStateType.TALK && newState !== AgentStateType.TALK) {
      console.log("通话状态结束，重置静音状态为false");
      isMuted.value = false;
    }

    // 如果状态变为HELD，确保不处于静音状态
    if (newState === AgentStateType.HELD && isMuted.value) {
      console.log("进入保持状态，重置静音状态为false");
      isMuted.value = false;
    }

    // 强制更新组件
    forceUpdateCounter.value += 1;
  },
  { immediate: true }
);

// 在全局事件监听器中添加调试日志
onMounted(() => {
  // 添加对代理状态变化的监听
  eventEmitter.on("agent:state", (event: any) => {
    console.log("代理状态改变事件:", event);
    if (event.state) {
      console.log(`事件通知代理状态变更为 ${event.state}`);
      // 如果状态变为非TALK，重置静音状态
      if (event.state !== AgentStateType.TALK && isMuted.value) {
        console.log("通话结束，重置静音状态");
        isMuted.value = false;
        forceUpdateCounter.value += 1;
      }
    }
  });

  // 添加静音相关事件监听
  eventEmitter.on("call:muted", (data: any) => {
    console.log("检测到call:muted事件", data);
    isMuted.value = true;
    forceUpdateCounter.value += 1;
    logButtonStates();

    // 触发按钮状态重新计算
    nextTick(() => {
      console.log("静音状态设置后，DOM已更新");
      logButtonStates();
    });
  });

  eventEmitter.on("call:unmuted", (data: any) => {
    console.log("检测到call:unmuted事件", data);
    isMuted.value = false;
    forceUpdateCounter.value += 1;
    logButtonStates();

    // 触发按钮状态重新计算
    nextTick(() => {
      console.log("取消静音状态设置后，DOM已更新");
      logButtonStates();
    });
  });

  // 初始化时检查静音状态
  console.log("组件挂载时的静音状态:", isMuted.value);
});

// 添加一个登录后的初始化方法
const onAfterLogin = async () => {
  console.log("用户已登录，CCBar已在挂载时自动初始化");

  // 不再需要在登录后调用初始化方法
  // 仅保留状态更新逻辑
  if (isLoggedIn.value) {
    // 之前可能有其他登录后的逻辑，保持不变
    console.log("用户登录后的处理");
  }
};
</script>

<style scoped>
.ccbar-toolbar {
  font-family: Arial, sans-serif;
  width: 100%;
  max-width: 100%;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.toolbar-toggle {
  position: absolute;
  right: 5px;
  top: 10px;
  cursor: pointer;
  font-size: 16px;
  color: #909399;
  z-index: 10;
}

.toolbar-main {
  display: flex;
  align-items: center;
  padding: 10px;
  flex-wrap: wrap;
}

.toolbar-status {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 200px;
}

.agent-info {
  margin-right: 15px;
}

.agent-state {
  display: flex;
  align-items: center;
  font-weight: bold;
  margin-bottom: 5px;
}

.state-icon {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 5px;
}

.state-ready .state-icon {
  background-color: #67c23a;
}

.state-busy .state-icon {
  background-color: #e6a23c;
}

.state-talking .state-icon {
  background-color: #409eff;
}

.state-ringing .state-icon {
  background-color: #409eff;
  animation: blink 1s infinite;
}

.state-held .state-icon {
  background-color: #909399;
}

.state-acw .state-icon {
  background-color: #909399;
}

.state-offline .state-icon {
  background-color: #f56c6c;
}

.agent-id {
  font-size: 12px;
  color: #606266;
}

.agent-outbound-number {
  font-size: 12px;
  color: #409eff;
  margin-top: 3px;
  background-color: #ecf5ff;
  padding: 2px 5px;
  border-radius: 3px;
  display: inline-block;
}

.state-controls {
  display: flex;
  position: relative;
}

.state-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 100;
  min-width: 100px;
}

.menu-item {
  padding: 8px 12px;
  cursor: pointer;
}

.menu-item:hover {
  background-color: #f5f7fa;
}

.toolbar-call {
  display: flex;
  flex-direction: column;
  margin: 0 15px;
  flex: 2;
  min-width: 250px;
}

.call-input {
  display: flex;
  margin-bottom: 10px;
}

.call-input input {
  flex: 1;
  margin-right: 10px;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.call-controls {
  display: flex;
}

.toolbar-login,
.toolbar-logout {
  margin-left: auto;
}

.toolbar-notification {
  padding: 10px;
  border-top: 1px solid #e4e7ed;
  background-color: #f5f7fa;
  font-size: 12px;
}

.event-item {
  display: flex;
  align-items: center;
}

.event-type {
  background-color: #409eff;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  margin-right: 8px;
  font-size: 10px;
}

.event-content {
  color: #606266;
}

/* 按钮样式 */
.btn {
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  margin-right: 5px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-ready {
  background-color: #67c23a;
  color: white;
}

.btn-not-ready {
  background-color: #e6a23c;
  color: white;
}

.btn-status {
  background-color: #909399;
  color: white;
}

.btn-call {
  background-color: #409eff;
  color: white;
}

.btn-hangup {
  background-color: #f56c6c;
  color: white;
}

.btn-hold {
  background-color: #909399;
  color: white;
}

.btn-retrieve {
  background-color: #67c23a;
  color: white;
  font-weight: bold;
}

.btn-login {
  background-color: #409eff;
  color: white;
}

.btn-logout {
  background-color: #909399;
  color: white;
}

.btn-mute {
  background-color: #409eff;
  color: white;
  position: relative;
}

.btn-mute::before {
  content: "🎤";
  margin-right: 4px;
}

.btn-unmute {
  background-color: #e6a23c;
  color: white;
  position: relative;
}

.btn-unmute::before {
  content: "🔇";
  margin-right: 4px;
}

/* 折叠样式 */
.is-expanded {
  min-height: 50px;
}

.is-expanded .toolbar-main {
  flex-direction: row;
}

@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 1;
  }
}

/* 小屏幕自适应 */
@media screen and (max-width: 768px) {
  .toolbar-main {
    flex-direction: column;
    align-items: stretch;
  }

  .toolbar-status,
  .toolbar-call,
  .toolbar-login,
  .toolbar-logout {
    margin: 5px 0;
  }

  .call-input {
    flex-direction: column;
  }

  .call-input input {
    margin-right: 0;
    margin-bottom: 5px;
  }
}

.agent-state-duration {
  font-size: 12px;
  color: #606266;
  margin-bottom: 5px;
}

/* 在HELD状态下突出显示恢复按钮 */
.state-held ~ .toolbar-call .btn-retrieve:not(:disabled) {
  animation: pulse 1.5s infinite;
  box-shadow: 0 0 5px #67c23a;
  background-color: #67c23a;
  transform-origin: center;
  font-weight: bold;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
