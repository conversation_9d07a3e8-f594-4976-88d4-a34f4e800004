import { CCBarResponse, LoginParams } from '../../types';

/**
 * 连接管理器接口
 * 负责管理座席登录、登出和会话维护
 */
export interface IConnectionManager {
  /**
   * 座席登录
   * @param params 登录参数
   * @returns 登录结果
   */
  login(params: LoginParams): Promise<CCBarResponse>;

  /**
   * 座席登出
   * @returns 登出结果
   */
  logout(): Promise<CCBarResponse>;

  /**
   * 检查是否已登录
   * @returns 是否已登录
   */
  isLoggedIn(): boolean;

  /**
   * 设置登录状态
   * @param isLogined 是否已登录
   */
  setLoginState(isLogined: boolean): void;

  /**
   * 获取会话ID
   * @returns 会话ID
   */
  getSessionId(): string;

  /**
   * 获取座席信息
   * @returns 座席信息
   */
  getAgentInfo(): any;

  initCCbar(): Promise<CCBarResponse>;
} 