import { CCBarResponse } from '../../types';

/**
 * 班长操作管理器接口
 * 负责班长/主管对座席的监听、强插、强拆、密语等操作
 */
export interface ISupervisorManager {
  /**
   * 更新会话ID
   * @param sessionId 新的会话ID
   * @deprecated sessionId 在班长功能中不再使用，但为了保持API兼容性而保留
   */
  updateSessionId(sessionId: string): void;
  
  /**
   * 开始监听
   * @param agentId 座席ID
   * @param callId 通话ID，不传则使用当前通话
   * @param options 附加选项
   * @returns 监听结果
   */
  startMonitor(agentId: string, callId?: string, options?: any): Promise<CCBarResponse>;
  
  /**
   * 结束监听
   * @param agentId 座席ID
   * @param callId 通话ID，不传则使用当前通话
   * @param options 附加选项
   * @returns 操作结果
   */
  stopMonitor(agentId: string, callId?: string, options?: any): Promise<CCBarResponse>;
  
  /**
   * 开始强插
   * @param agentId 座席ID
   * @param callId 通话ID，不传则使用当前通话
   * @param options 附加选项
   * @returns 操作结果
   */
  startIntercept(agentId: string, callId?: string, options?: any): Promise<CCBarResponse>;
  
  /**
   * 结束强插
   * @param agentId 座席ID
   * @param callId 通话ID，不传则使用当前通话
   * @param options 附加选项
   * @returns 操作结果
   */
  stopIntercept(agentId: string, callId?: string, options?: any): Promise<CCBarResponse>;
  
  /**
   * 开始强拆
   * @param agentId 座席ID
   * @param callId 通话ID，不传则使用当前通话
   * @param options 附加选项
   * @returns 操作结果
   */
  startIntercept(agentId: string, callId?: string, options?: any): Promise<CCBarResponse>;
  /**
   * 强制置忙
   * @param agentId 座席ID
   * @param reason 原因，可选
   * @param options 附加选项
   * @returns 操作结果
   */
  forceBusy(agentId: string, reason?: string, options?: any): Promise<CCBarResponse>;
  
  /**
   * 强制置闲
   * @param agentId 座席ID
   * @param options 附加选项
   * @returns 操作结果
   */
  forceIdle(agentId: string, options?: any): Promise<CCBarResponse>;
  
  /**
   * 开始密语
   * @param agentId 座席ID
   * @param callId 通话ID，不传则使用当前通话
   * @param options 附加选项
   * @returns 操作结果
   */
  startWhisper(agentId: string, callId?: string, options?: any): Promise<CCBarResponse>;
  
  /**
   * 结束密语
   * @param agentId 座席ID
   * @param callId 通话ID，不传则使用当前通话
   * @param options 附加选项
   * @returns 操作结果
   */
  stopWhisper(agentId: string, callId?: string, options?: any): Promise<CCBarResponse>;
  
  /**
   * 拦截通话
   * @param agentId 要拦截的座席ID
   * @param callId 通话ID，不传则使用当前通话
   * @param options 附加选项
   * @returns 操作结果
   */
  interceptCall(agentId: string, callId?: string, options?: any): Promise<CCBarResponse>;
  
  /**
   * 强制签出
   * @param agentId 座席ID
   * @param reason 原因，可选
   * @param options 附加选项
   * @returns 操作结果
   */
  forceLogout(agentId: string, reason?: string, options?: any): Promise<CCBarResponse>;
} 