# CCBar - 现代呼叫中心话务条

CCBar是一个基于Vue 3和TypeScript开发的现代化呼叫中心工具条组件，为呼叫中心座席提供轻量级、高效的操作界面。通过模块化设计和事件驱动架构，CCBar提供了完整的呼叫中心座席功能，包括登录、状态管理、呼叫控制、通话转接等核心功能。

## 目录

- [功能特点](#功能特点)
- [系统架构](#系统架构)
- [安装](#安装)
- [使用方法](#使用方法)
  - [作为Vue组件使用](#作为vue组件使用)
  - [使用CCBarService](#使用ccbarservice)
  - [导入单个组件](#导入单个组件)
- [配置选项](#配置选项)
- [API文档](#api文档)
- [项目结构](#项目结构)
- [开发指南](#开发指南)
- [浏览器兼容性](#浏览器兼容性)
- [常见问题](#常见问题)
- [更新日志](#更新日志)

## 功能特点

- **模块化设计**：核心功能被分解为独立模块，便于维护和扩展
- **事件驱动架构**：基于发布-订阅模式，组件间松耦合通信
- **多通信方式**：支持WebSocket实时通信和HTTP轮询两种方式
- **完整状态管理**：管理座席状态、功能掩码和通话状态
- **呼叫控制**：支持拨号、应答、挂断、保持等核心呼叫功能
- **通话转接**：支持座席转接和电话号码转接
- **现代UI设计**：简洁、直观的用户界面，支持折叠/展开
- **TypeScript支持**：完整的类型定义，提供代码提示和编译时检查
- **全局状态同步**：确保座席状态在多组件间一致性

## 系统架构

CCBar采用分层架构设计，主要由以下几个核心模块组成：

1. **连接管理模块** (ConnectionManager)
   - 负责座席登录、登出和会话维护
   - 管理WebSocket连接或HTTP轮询通信

2. **状态管理模块** (StateManager)
   - 管理座席状态（就绪、未就绪、通话中等）
   - 处理功能掩码和权限控制

3. **呼叫管理模块** (CallManager)
   - 负责处理呼叫相关操作（拨号、应答、挂断等）
   - 管理通话状态和呼叫事件

4. **转接管理模块** (TransferManager)
   - 处理呼叫转接和咨询功能
   - 支持座席转接和电话号码转接

5. **事件管理模块** (EventManager)
   - 提供发布-订阅模式的事件总线
   - 处理系统内部和外部事件通信

6. **全局状态管理器** (GlobalStateManager)
   - 统一管理整个系统的状态
   - 确保状态一致性和稳定性

## 安装

```bash
# 使用npm安装
npm install ccbar

# 使用yarn安装
yarn add ccbar

# 使用pnpm安装
pnpm add ccbar
```

## 使用方法

### 作为Vue组件使用

最简单的使用方式是将CCBar作为Vue组件直接引入：

```vue
<template>
  <cc-bar :config="ccbarConfig" 
          @state-changed="handleStateChange"
          @login="handleLogin"
          @logout="handleLogout"
          @call-connected="handleCallEvent"
          @call-disconnected="handleCallEvent"/>
</template>

<script setup>
import { reactive } from 'vue';
import { CCBar } from 'ccbar';

// 配置CCBar
const ccbarConfig = reactive({
  baseURL: '/api/ccbar',    // API基础地址
  wsURL: '/ws',             // WebSocket地址
  entId: '123456',          // 企业ID
  loginKey: 'xxxxxxxx',     // 登录密钥
  productId: '100000',      // 产品ID
  debug: true               // 开启调试模式
});

// 状态变更处理
const handleStateChange = (state) => {
  console.log('座席状态变更:', state);
};

// 登录事件处理
const handleLogin = (data) => {
  console.log('登录成功:', data);
};

// 登出事件处理
const handleLogout = () => {
  console.log('登出成功');
};

// 呼叫事件处理
const handleCallEvent = (event) => {
  console.log('呼叫事件:', event);
};
</script>
```

### 使用CCBarService

你也可以直接使用CCBarService进行更细粒度的控制：

```javascript
import { CCBar } from 'ccbar';

// 初始化CCBar服务
const ccbarService = CCBar.getInstance({
  baseURL: '/api/ccbar',
  wsURL: '/ws',
  debug: true
});

// 登录座席
async function login() {
  try {
    const result = await ccbarService.login({
      username: '1001',
      password: 'password',
      phone: '55552013'
    });
    
    if (result.state) {
      console.log('登录成功');
    } else {
      console.error('登录失败:', result.msg);
    }
  } catch (error) {
    console.error('登录异常:', error);
  }
}

// 监听来电事件
ccbarService.on('call:incoming', (data) => {
  console.log('收到来电:', data);
});

// 拨打电话
async function makeCall(phoneNumber) {
  try {
    const result = await ccbarService.makeCall(phoneNumber);
    console.log('拨号结果:', result);
  } catch (error) {
    console.error('拨号失败:', error);
  }
}

// 应答来电
async function answerCall() {
  try {
    await ccbarService.answerCall();
  } catch (error) {
    console.error('应答失败:', error);
  }
}

// 挂断通话
async function hangupCall() {
  try {
    await ccbarService.clearCall();
  } catch (error) {
    console.error('挂断失败:', error);
  }
}
```

### 导入单个组件

你还可以单独导入CCBar的子组件：

```vue
<template>
  <div>
    <!-- 工具条组件 -->
    <cc-bar-toolbar 
      :config="config" 
      @state-changed="handleStateChange"
      @call-event="handleCallEvent"
      @show-login="showLoginDialog = true"/>
    
    <!-- 登录弹窗组件 -->
    <cc-bar-login
      :config="config"
      :visible="showLoginDialog"
      @close="showLoginDialog = false"
      @login-success="handleLoginSuccess"/>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { CCBarToolbar, CCBarLogin } from 'ccbar';

const config = {
  baseURL: '/api/ccbar',
  wsURL: '/ws'
};

const showLoginDialog = ref(false);

function handleStateChange(state) {
  console.log('状态变更:', state);
}

function handleCallEvent(event) {
  console.log('呼叫事件:', event);
}

function handleLoginSuccess(data) {
  showLoginDialog.value = false;
  console.log('登录成功:', data);
}
</script>
```

## 配置选项

CCBar提供了多种配置选项，可以根据实际需求进行定制：

| 选项 | 类型 | 默认值 | 说明 |
|------|------|-------|------|
| baseURL | string | - | API服务基础URL |
| wsURL | string | - | WebSocket服务URL |
| timeout | number | 10000 | 请求超时时间(ms) |
| autoReconnect | boolean | true | 是否自动重连 |
| maxReconnectAttempts | number | 5 | 最大重连尝试次数 |
| reconnectInterval | number | 3000 | 重连间隔时间(ms) |
| heartbeatInterval | number | 30000 | 心跳包发送间隔(ms) |
| autoReady | boolean | false | 登录后是否自动置为就绪 |
| pollingInterval | number | 3000 | HTTP轮询间隔时间(ms) |
| entId | string | - | 企业ID |
| loginKey | string | - | 登录密钥 |
| productId | string | - | 产品ID |
| debug | boolean | false | 是否开启调试模式 |

## API文档

### CCBarService

CCBarService是CCBar的核心服务类，提供了所有功能的接口：

#### 连接管理

- `login(params: LoginParams): Promise<CCBarResponse>`
  - 座席登录
  - 参数：登录信息，包含username, password, phone等

- `logout(): Promise<CCBarResponse>`
  - 座席登出

#### 状态管理

- `agentReady(): Promise<CCBarResponse>`
  - 座席置闲/就绪

- `agentNotReady(busyType?: string): Promise<CCBarResponse>`
  - 座席置忙/未就绪
  - 参数：busyType - 忙碌类型（可选）

- `workNotReady(): Promise<CCBarResponse>`
  - 设置话后整理状态

- `workReady(): Promise<CCBarResponse>`
  - 完成话后整理

- `setWorkMode(mode: WorkModeType): Promise<CCBarResponse>`
  - 设置工作模式
  - 参数：mode - 工作模式，如INBOUND, OUTBOUND, ALL

- `getState(): string`
  - 获取当前座席状态类型

- `getAgentInfo(): any`
  - 获取座席信息，包含工号、话机号等

#### 呼叫管理

- `makeCall(phoneNumber: string, displayNumber?: string, userData?: any, callType?: number): Promise<CCBarResponse>`
  - 拨打电话
  - 参数：
    - phoneNumber: 目标电话号码
    - displayNumber: 外显号码（可选）
    - userData: 用户自定义数据（可选）
    - callType: 呼叫类型（可选）

- `answerCall(): Promise<CCBarResponse>`
  - 应答来电

- `clearCall(callData?: any): Promise<CCBarResponse>`
  - 挂断呼叫
  - 参数：callData - 呼叫相关数据（可选）

- `holdCall(): Promise<CCBarResponse>`
  - 保持通话

- `unholdCall(): Promise<CCBarResponse>`
  - 取回通话

#### 转接管理

- `transferCall(params: TransferCallParams): Promise<CCBarResponse>`
  - 转接通话
  - 参数：转接参数，包含targetType, agentId/phoneNumber等

- `completeTransfer(): Promise<CCBarResponse>`
  - 完成转接

- `cancelTransfer(): Promise<CCBarResponse>`
  - 取消转接

#### 事件管理

- `on(eventName: string, callback: Function): void`
  - 订阅事件
  - 参数：
    - eventName: 事件名称
    - callback: 回调函数

- `off(eventName: string, callback: Function): void`
  - 取消订阅事件
  - 参数：
    - eventName: 事件名称
    - callback: 回调函数

- `once(eventName: string, callback: Function): void`
  - 一次性订阅事件
  - 参数：
    - eventName: 事件名称
    - callback: 回调函数

- `emit(eventName: string, data?: any): void`
  - 触发事件
  - 参数：
    - eventName: 事件名称
    - data: 事件数据（可选）

### 常用事件类型

CCBar提供了多种事件类型，可以通过on方法进行监听：

- `call:incoming` - 来电事件
- `call:established` - 通话建立事件
- `call:ended` - 通话结束事件
- `agent:stateChanged` - 座席状态变更事件
- `agent:loggedIn` - 座席登录成功事件
- `agent:loggedOut` - 座席登出事件

## 项目结构

```
src/
├── api/              # API服务层
│   ├── http.ts       # HTTP请求服务
│   ├── polling.ts    # HTTP轮询服务
│   └── websocket.ts  # WebSocket服务
├── components/       # Vue组件
│   ├── CCBar.vue            # 主组件（集成所有功能）
│   ├── CCBarToolbar.vue     # 工具条组件
│   ├── CCBarLogin.vue       # 登录组件
│   ├── TransferCallDialog.vue # 转接对话框组件
│   └── IncomingCallAlert.vue  # 来电提醒组件
├── core/             # 核心业务逻辑
│   ├── CCBarService.ts      # 核心服务类
│   ├── EventEmitter.ts      # 事件发射器
│   ├── GlobalStateManager.ts # 全局状态管理器
│   └── modules/             # 功能模块
│       ├── ConnectionManager.ts # 连接管理
│       ├── StateManager.ts      # 状态管理
│       ├── CallManager.ts       # 呼叫管理
│       ├── TransferManager.ts   # 转接管理
│       └── EventManager.ts      # 事件管理
├── examples/         # 示例代码
├── hooks/            # Vue组合式API钩子
├── utils/            # 工具函数
├── types/            # 类型定义
└── index.ts          # 库导出入口
```

## 开发指南

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

## 浏览器兼容性

- Chrome 60+
- Firefox 52+
- Safari 11+
- Edge 79+

## 常见问题

**问题1：如何处理登录失败？**

登录失败可能有多种原因，常见的包括网络问题、凭据错误或服务器问题。建议实现错误处理逻辑并提供友好的错误提示。例如：

```javascript
const result = await ccbarService.login({
  username: '1001',
  password: 'password',
  phone: '55552013'
});

if (!result.state) {
  // 根据错误代码显示不同的错误信息
  switch (result.data.code) {
    case 'auth_failed':
      alert('用户名或密码错误');
      break;
    case 'server_error':
      alert('服务器错误，请稍后重试');
      break;
    default:
      alert(`登录失败: ${result.msg}`);
  }
}
```

**问题2：WebSocket连接断开如何处理？**

CCBar内置了重连机制，但你也可以监听连接状态事件：

```javascript
ccbarService.on('connection:disconnected', () => {
  console.warn('WebSocket连接已断开，正在尝试重连...');
});

ccbarService.on('connection:reconnected', () => {
  console.log('WebSocket连接已恢复');
});
```

**问题3：如何保持用户登录状态？**

CCBar会在localStorage中保存会话信息。你可以在应用初始化时检查登录状态：

```javascript
// 检查是否已登录
const isLoggedIn = ccbarService.isLoggedIn();

if (isLoggedIn) {
  console.log('用户已登录，座席ID:', ccbarService.getAgentId());
} else {
  console.log('用户未登录，显示登录界面');
}
```

## 更新日志

### 1.0.0 (2024-07-01)
- 初始版本发布
- 基本功能实现：登录登出、状态管理、呼叫控制、转接功能
- 支持WebSocket和HTTP轮询两种通信方式
