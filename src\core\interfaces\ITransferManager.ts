import { CCBarResponse, TransferCallParams } from '../../types';

/**
 * 转接管理器接口
 * 负责管理呼叫转接
 */
export interface ITransferManager {
  /**
   * 更新会话ID
   * @param sessionId 新的会话ID
   */
  updateSessionId(sessionId: string): void;

  /**
   * 更新当前呼叫ID
   * @param callId 呼叫ID
   */
  updateCurrentCallId(callId: string): void;

  /**
   * 发起呼叫转接
   * @param params 转接参数
   * @returns 转接结果
   */
  transferCall(params: TransferCallParams): Promise<CCBarResponse>;

  /**
   * 完成转接
   * @returns 完成转接结果
   */
  completeTransfer(): Promise<CCBarResponse>;

  /**
   * 取消转接
   * @returns 取消转接结果
   */
  cancelTransfer(): Promise<CCBarResponse>;

  /**
   * 获取转接状态
   * @returns 是否正在转接中
   */
  isInTransfer(): boolean;
} 