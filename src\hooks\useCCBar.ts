import { ref, reactive, onMounted, onBeforeUnmount } from 'vue';
import { CCBar } from '../index';

/**
 * CCBar组合式API钩子
 * 提供对CCBar功能的便捷访问
 * 
 * @param options 可选配置项
 * @returns CCBar相关的状态和方法
 */
export function useCCBar(options?: {
  autoInit?: boolean;
  baseURL?: string;
  wsURL?: string;
  usePolling?: boolean;
  pollingInterval?: number;
  debug?: boolean;
  onEvent?: (eventName: string, data: any) => void;
}) {
  // 默认配置
  const defaultOptions = {
    autoInit: false,
    usePolling: false,
    pollingInterval: 5000,
    debug: false
  };

  const mergedOptions = { ...defaultOptions, ...options };
  
  // 状态
  const isLoggedIn = ref(false);
  const isConnected = ref(false);
  const agentState = ref('');
  const callInfo = reactive({
    isInCall: false,
    isRinging: false,
    phoneNumber: '',
    callerId: '',
    callerName: '',
    startTime: null as Date | null,
    direction: ''
  });
  const loading = ref(false);
  const error = ref<Error | null>(null);
  
  /**
   * 初始化服务
   */
  const init = () => {
    if (mergedOptions.baseURL) {
      CCBar.service.baseURL = mergedOptions.baseURL;
    }
    
    if (mergedOptions.wsURL) {
      CCBar.service.wsURL = mergedOptions.wsURL;
    }
    
    if (mergedOptions.debug) {
      CCBar.service.enableDebug();
    }
  };
  
  /**
   * 登录
   * @param params 登录参数
   */
  const login = async (params: {
    agentId: string;
    password: string;
    extension?: string;
    usePolling?: boolean;
  }) => {
    loading.value = true;
    error.value = null;
    
    try {
      const usePolling = params.usePolling !== undefined 
        ? params.usePolling 
        : mergedOptions.usePolling;
      
      const result = await CCBar.service.login({
        ...params,
        usePolling,
        pollingConfig: usePolling ? {
          pollingInterval: mergedOptions.pollingInterval
        } : undefined
      });
      
      isLoggedIn.value = true;
      return result;
    } catch (err) {
      error.value = err as Error;
      console.error('登录失败:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };
  
  /**
   * 登出
   */
  const logout = async () => {
    loading.value = true;
    error.value = null;
    
    try {
      await CCBar.service.logout();
      isLoggedIn.value = false;
      agentState.value = '';
      resetCallInfo();
    } catch (err) {
      error.value = err as Error;
      console.error('登出失败:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };
  
  /**
   * 设置座席状态
   * @param state 状态值
   */
  const setAgentState = async (state: string) => {
    loading.value = true;
    error.value = null;
    
    try {
      await CCBar.service.setAgentState(state);
      agentState.value = state;
    } catch (err) {
      error.value = err as Error;
      console.error('设置状态失败:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };
  
  /**
   * 拨打电话
   * @param phoneNumber 电话号码
   */
  const makeCall = async (phoneNumber: string) => {
    loading.value = true;
    error.value = null;
    
    try {
      await CCBar.service.makeCall(phoneNumber);
      
      callInfo.isInCall = true;
      callInfo.isRinging = false;
      callInfo.phoneNumber = phoneNumber;
      callInfo.startTime = new Date();
      callInfo.direction = 'outbound';
    } catch (err) {
      error.value = err as Error;
      console.error('拨打电话失败:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };
  
  /**
   * 接听电话
   */
  const answerCall = async () => {
    loading.value = true;
    error.value = null;
    
    try {
      await CCBar.service.answerCall();
      
      callInfo.isInCall = true;
      callInfo.isRinging = false;
      callInfo.startTime = new Date();
    } catch (err) {
      error.value = err as Error;
      console.error('接听电话失败:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };
  
  /**
   * 挂断电话
   */
  const hangupCall = async () => {
    loading.value = true;
    error.value = null;
    
    try {
      await CCBar.service.hangupCall();
      resetCallInfo();
    } catch (err) {
      error.value = err as Error;
      console.error('挂断电话失败:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };
  
  /**
   * 保持通话
   */
  const holdCall = async () => {
    loading.value = true;
    error.value = null;
    
    try {
      await CCBar.service.holdCall();
    } catch (err) {
      error.value = err as Error;
      console.error('保持通话失败:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };
  
  /**
   * 恢复通话
   */
  const resumeCall = async () => {
    loading.value = true;
    error.value = null;
    
    try {
      await CCBar.service.resumeCall();
    } catch (err) {
      error.value = err as Error;
      console.error('恢复通话失败:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };
  
  /**
   * 转接电话
   * @param targetNumber 转接目标号码
   */
  const transferCall = async (targetNumber: string) => {
    loading.value = true;
    error.value = null;
    
    try {
      await CCBar.service.transferCall(targetNumber);
    } catch (err) {
      error.value = err as Error;
      console.error('转接电话失败:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };
  
  /**
   * 重置通话信息
   */
  const resetCallInfo = () => {
    callInfo.isInCall = false;
    callInfo.isRinging = false;
    callInfo.phoneNumber = '';
    callInfo.callerId = '';
    callInfo.callerName = '';
    callInfo.startTime = null;
    callInfo.direction = '';
  };
  
  /**
   * 监听事件
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  const on = (eventName: string, callback: Function) => {
    CCBar.service.on(eventName, callback);
  };
  
  /**
   * 移除事件监听
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  const off = (eventName: string, callback: Function) => {
    CCBar.service.off(eventName, callback);
  };
  
  /**
   * 一次性事件监听
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  const once = (eventName: string, callback: Function) => {
    CCBar.service.once(eventName, callback);
  };
  
  // 设置事件监听
  const setupEventListeners = () => {
    // 连接事件
    CCBar.service.on('ws:open', () => {
      isConnected.value = true;
    });
    
    CCBar.service.on('ws:close', () => {
      isConnected.value = false;
    });
    
    // 座席状态变更
    CCBar.service.on('agent:stateChanged', (data: any) => {
      agentState.value = data.state;
    });
    
    // 来电事件
    CCBar.service.on('call:incoming', (data: any) => {
      callInfo.isRinging = true;
      callInfo.phoneNumber = data.phoneNumber;
      callInfo.callerId = data.callerId || '';
      callInfo.callerName = data.callerName || '';
      callInfo.direction = 'inbound';
    });
    
    // 通话建立
    CCBar.service.on('call:connected', (data: any) => {
      callInfo.isInCall = true;
      callInfo.isRinging = false;
      callInfo.startTime = new Date();
      
      // 如果是外呼接通，更新电话号码
      if (data.phoneNumber) {
        callInfo.phoneNumber = data.phoneNumber;
      }
    });
    
    // 通话结束
    CCBar.service.on('call:ended', () => {
      resetCallInfo();
    });
    
    // 会话超时
    CCBar.service.on('sessionTimeout', () => {
      isLoggedIn.value = false;
      agentState.value = '';
      resetCallInfo();
    });
    
    // 如果提供了自定义事件处理函数，将所有事件转发过去
    if (mergedOptions.onEvent) {
      CCBar.service.on('*', (eventName: string, data: any) => {
        mergedOptions.onEvent?.(eventName, data);
      });
    }
  };
  
  // 在组件挂载时初始化
  onMounted(() => {
    init();
    setupEventListeners();
    
    if (mergedOptions.autoInit) {
      // 如果设置了自动初始化，可以在这里执行其他初始化逻辑
    }
  });
  
  // 在组件卸载前清理
  onBeforeUnmount(() => {
    // 清理事件监听
    CCBar.service.off('ws:open');
    CCBar.service.off('ws:close');
    CCBar.service.off('agent:stateChanged');
    CCBar.service.off('call:incoming');
    CCBar.service.off('call:connected');
    CCBar.service.off('call:ended');
    CCBar.service.off('sessionTimeout');
    
    if (mergedOptions.onEvent) {
      CCBar.service.off('*');
    }
    
    // 如果登录状态下卸载组件，确保登出
    if (isLoggedIn.value) {
      CCBar.service.logout().catch(console.error);
    }
  });
  
  return {
    // 状态
    isLoggedIn,
    isConnected,
    agentState,
    callInfo,
    loading,
    error,
    
    // 方法
    login,
    logout,
    setAgentState,
    makeCall,
    answerCall,
    hangupCall,
    holdCall,
    resumeCall,
    transferCall,
    
    // 事件
    on,
    off,
    once,
    
    // 服务实例
    service: CCBar.service
  };
} 