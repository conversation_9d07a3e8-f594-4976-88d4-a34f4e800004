<!--
CCBarLogin.vue - CCBar登录组件

这是话务条的配套登录组件，提供简洁的登录界面，包含以下功能：
1. 基本的账号密码登录
2. 可配置的高级选项（话机号、企业ID等）
3. 登录状态提示

使用方法：
1. 引入组件并注册
   ```javascript
   import CCBarLogin from 'path/to/CCBarLogin.vue';
   
   export default {
     components: {
       CCBarLogin
     }
   }
   ```

2. 在模板中使用
   ```html
   <CCBarLogin
     :config="ccbarConfig"
     :visible="showLoginDialog"
     @close="showLoginDialog = false"
     @login-success="handleLoginSuccess"
   />
   ```
-->

<template>
  <div class="login-overlay" v-if="visible" @click.self="$emit('close')">
    <div class="login-dialog">
      <div class="login-header">
        <h3>话务员登录</h3>
        <button class="close-btn" @click="$emit('close')">×</button>
      </div>

      <div class="login-body">
        <div class="login-form">
          <!-- <div class="form-group">
            <label>工号</label>
            <input type="text" v-model="loginForm.agentId" placeholder="请输入工号" />
          </div>
          
          <div class="form-group">
            <label>密码</label>
            <input type="password" v-model="loginForm.password" placeholder="请输入密码" />
          </div>
           -->
          <div class="form-group">
            <label>话机号码</label>
            <input
              type="text"
              v-model="loginForm.phone"
              placeholder="请输入话机号"
            />
          </div>

          <!-- 添加技能组多选框 -->
          <div class="form-group skills-group">
            <label>技能组选择</label>
            <div class="skills-container">
              <div
                v-for="(skill, index) in props.skills"
                :key="index"
                class="skill-item"
              >
                <label class="skill-label">
                  <input
                    type="checkbox"
                    :value="getSkillValue(skill)"
                    v-model="loginForm.selectedSkills"
                  />
                  <span>{{ getSkillName(skill) }}</span>
                </label>
              </div>
            </div>
          </div>

          <!--           
          <div v-if="showAdvanced" class="advanced-options">
            <div class="form-group">
              <label>企业ID</label>
              <input type="text" v-model="loginForm.entId" placeholder="请输入企业ID" />
            </div>
            
            <div class="form-group">
              <label>产品ID</label>
              <input type="text" v-model="loginForm.productId" placeholder="请输入产品ID" />
            </div>
            
            <div class="form-group">
              <label>工作模式</label>
              <select v-model="loginForm.workMode">
                <option value="preview">预览外呼</option>
                <option value="auto">自动外呼</option>
                <option value="all">全部</option>
              </select>
            </div>
            
            <div class="form-group">
              <label>就绪模式</label>
              <select v-model="loginForm.readyMode">
                <option value="autoReady">自动就绪</option>
                <option value="notReady">手动就绪</option>
              </select>
            </div>
            
            <div class="form-group checkbox-group">
              <label>
                <input type="checkbox" v-model="loginForm.autoAnswer" />
                自动应答
              </label>
            </div>
            
            <div class="form-group checkbox-group">
              <label>
                <input type="checkbox" v-model="loginForm.force" />
                强制登录
              </label>
            </div>
          </div>
          
          <div class="toggle-advanced" @click="showAdvanced = !showAdvanced">
            {{ showAdvanced ? '隐藏高级选项' : '显示高级选项' }}
          </div> -->
        </div>
      </div>

      <div class="login-footer">
        <div v-if="errorMessage" class="error-message">{{ errorMessage }}</div>
        <button class="login-btn" @click="handleLogin" :disabled="loading">
          {{ loading ? "登录中..." : "登录" }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { CCBarService } from "../core/CCBarService";
import { LoginParams } from "../types";

// 定义组件属性
const props = defineProps({
  config: {
    type: Object,
    required: true,
  },
  visible: {
    type: Boolean,
    default: false,
  },
  skills: {
    type: Array,
    required: true,
  },
});

// 定义组件事件
const emit = defineEmits(["close", "login-success"]);

// 获取CCBarService实例
const ccbarService = CCBarService.getInstance(props.config);

// 状态变量
const loading = ref(false);
const errorMessage = ref("");
const showAdvanced = ref(false);

// 登录表单
const loginForm = reactive({
  agentId: "",
  password: "",
  phone: "55552013", // 默认话机号
  entId: "",
  loginKey: "",
  productId: "",
  workMode: "preview",
  readyMode: "notReady",
  autoAnswer: false,
  force: false,
  selectedSkills: [] as string[], // 技能组ID数组
});

// 组件挂载
onMounted(() => {
  // 初始化表单中的企业相关配置
  if (props.config) {
    loginForm.entId = props.config.entId || "";
    loginForm.loginKey = props.config.loginKey || "";
    loginForm.productId = props.config.productId || "";
  }
});

// 登录处理函数
const handleLogin = async () => {
  // 表单验证
  try {
    loading.value = true;
    errorMessage.value = "";

    // 构建登录参数
    const loginParams: LoginParams = {
      username: loginForm.agentId,
      password: loginForm.password,
      phone: loginForm.phone,
      phoneType: "softphone",
      loginType: 0,
      readyMode: loginForm.readyMode,
      workMode: loginForm.workMode,
      autoAnswer: loginForm.autoAnswer,
      force: loginForm.force,
      skillId: loginForm.selectedSkills.join(";"),
    };

    // 调用CCBarService实例的login方法

    const result = await ccbarService.login(loginParams);
    if (result.state) {
      // 登录成功，保存话机号到localStorage
      localStorage.setItem("phone", loginForm.phone);
      
      // 发送事件并关闭对话框
      emit("login-success", {
        agentId: loginForm.agentId || localStorage.getItem("agentId") || "",
        phone: loginForm.phone || localStorage.getItem("phone") || "",
      });
      emit("close");
    } else {
      // 登录失败，显示错误信息
      errorMessage.value = `登录失败: ${result.msg || "未知错误"}`;
    }
  } catch (error: any) {
    errorMessage.value = `登录异常: ${error.message || "未知错误"}`;
    console.error("登录异常:", error);
  } finally {
    loading.value = false;
  }
};

// 获取技能值的辅助函数
const getSkillValue = (skill: any): string => {
  return typeof skill === "object"
    ? skill.SKILL_GROUP_ID || skill.value || ""
    : String(skill);
};

// 获取技能名称的辅助函数
const getSkillName = (skill: any): string => {
  return typeof skill === "object"
    ? skill.SKILL_GROUP_NAME || skill.label || ""
    : String(skill);
};
</script>

<style scoped>
.login-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.login-dialog {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: 400px;
  max-width: 90%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.login-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e4e7ed;
}

.login-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  color: #909399;
  cursor: pointer;
  padding: 0;
}

.login-body {
  padding: 20px;
  /* overflow-y: auto; */
}

.login-form {
  display: flex;
  flex-direction: column;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #606266;
}

.form-group input[type="text"],
.form-group input[type="password"],
.form-group select {
  width: 100%;
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #409eff;
}

.checkbox-group {
  display: flex;
  align-items: center;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  margin-bottom: 0;
  cursor: pointer;
}

.checkbox-group input[type="checkbox"] {
  margin-right: 5px;
}

.advanced-options {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px dashed #e4e7ed;
}

.toggle-advanced {
  color: #409eff;
  font-size: 12px;
  cursor: pointer;
  text-align: right;
  margin-top: 5px;
}

.login-footer {
  padding: 15px 20px;
  border-top: 1px solid #e4e7ed;
  text-align: right;
}

.error-message {
  color: #f56c6c;
  font-size: 12px;
  margin-bottom: 10px;
  text-align: left;
}

.login-btn {
  background-color: #409eff;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  padding: 10px 20px;
  font-size: 14px;
}

.login-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.login-btn:not(:disabled):hover {
  background-color: #66b1ff;
}

.skills-group {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px dashed #e4e7ed;
}

.skills-container {
  display: flex;
  flex-direction: column;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 6px 2px;
  max-height: 180px;
  overflow-y: auto;
  /* background-color: #f9f9f9; */
}

.skill-item {
  margin-bottom: 2px;
  padding: 5px 8px;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.skill-item:last-child {
  margin-bottom: 0;
}

.skill-item:hover {
  /* background-color: #eef5fe; */
  /* color: #eef5fe; */
}

.skill-label {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  cursor: pointer;
  width: 100%;
  font-size: 13px;
  height: 24px; /* 设置固定高度确保对齐 */
}

.skill-label input[type="checkbox"] {
  margin-right: 8px;
  position: relative;
  top: 0;
}

.skill-label input[type="checkbox"]:checked + span {
  color: #409eff;
}

.skill-label span {
  color: #606266;
  display: inline-block;
  line-height: 1.2;
}

.skills-group label {
  color: #606266;
  font-size: 14px;
}
</style>
