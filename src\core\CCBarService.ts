/**
 * CCBar服务 - 核心服务类
 * 功能模块化设计：
 * 1. 连接管理模块 - 处理登录、登出、会话维护
 * 2. 状态管理模块 - 处理座席状态变更
 * 3. 呼叫管理模块 - 处理呼叫相关操作
 * 4. 事件管理模块 - 处理事件订阅与分发
 * 5. 转接管理模块 - 处理呼叫转接相关功能
 */

import {
  AgentStateType,
  WorkModeType,
  FuncMaskType,
  CallEventType,
  NotifyEventType,
} from '../types';
import type {
  LoginParams,
  CCBarResponse,
  CCBarConfig,
  AgentState,
  CallEvent,
  NotifyEvent,
  TransferCallParams
} from '../types';

import { ApiResponse } from '../types/api';

// 核心模块导入
import { EventEmitter } from './EventEmitter';
import { RequestService } from '../utils/request';
import { WebSocketService } from '../utils/websocket';
import { hashMd5, ccbarDebugger } from '../utils';

// 全局状态管理器导入
import GlobalStateManager from './GlobalStateManager';

// 导入配置工具
import { initializeWithConfig } from '../utils/config';

// 导入接口定义
import {
  IConnectionManager,
  IStateManager,
  ICallManager,
  IEventManager,
  ITransferManager,
  ISupervisorManager
} from './interfaces';

// 导入模块实现
import {
  ConnectionManager,
  StateManager,
  CallManager,
  EventManager,
  TransferManager
} from './modules';

// 导入班长操作管理器
import { SupervisorManager } from './modules/SupervisorManager';

/**
 * CCBar核心服务类 - 采用模块化设计
 * 职责：协调各功能模块，提供统一的服务接口
 */
export class CCBarService {
  private static instance: CCBarService;

  // 内部服务模块
  private connectionManager!: IConnectionManager;
  private stateManager!: IStateManager;
  private callManager!: ICallManager;
  private eventManager!: IEventManager;
  private transferManager!: ITransferManager;
  private supervisorManager!: ISupervisorManager;

  // 全局状态管理器
  private globalStateManager!: GlobalStateManager;

  // 配置
  private config: CCBarConfig;

  /**
   * 私有构造函数，防止外部直接实例化
   * @param config 配置参数
   */
  private constructor(config: CCBarConfig) {
    this.config = config;

    // 初始化全局状态管理器
    this.globalStateManager = GlobalStateManager.getInstance();

    // 初始化真实模块
    this.initializeRealModules(config);

    // 设置全局状态事件监听
    this.setupGlobalStateEventListeners();
  }

  /**
   * 初始化真实模块
   * @param config 配置参数
   */
  private initializeRealModules(config: CCBarConfig): void {
    console.log('[CCBarService] 初始化模块，配置:', config);

    // 确保配置对象有默认值
    const defaultConfig = {
      baseURL: 'http://localhost:8080',
      wsURL: 'ws://localhost:8081',
      ...config
    };

    // 创建事件发射器
    const eventEmitter = EventEmitter.getInstance();

    // 创建请求服务
    const requestService = new RequestService(
      defaultConfig.timeout || 10000,
      true
    );

    // 创建事件管理器 - 必须在其他模块之前创建
    this.eventManager = new EventManager(eventEmitter);
    console.log('[CCBarService] 事件管理器已创建:', this.eventManager);

    // 强制检查是否应该禁用WebSocket
    const shouldDisableWS = defaultConfig.serviceType === 'polling' ||
      defaultConfig.disableWebSocket === true ||
      !defaultConfig.wsURL ||
      defaultConfig.wsURL === '';

    console.log('[CCBarService] WebSocket禁用检查:', {
      serviceType: defaultConfig.serviceType,
      disableWebSocket: defaultConfig.disableWebSocket,
      wsURL: defaultConfig.wsURL,
      shouldDisableWS
    });

    // 只在需要WebSocket时才创建WebSocket服务
    let wsService: WebSocketService | null = null;
    if (!shouldDisableWS) {
      console.log('[CCBarService] 创建WebSocket服务');
      wsService = new WebSocketService(
        defaultConfig.wsURL || '',
        {
          reconnectInterval: defaultConfig.reconnectInterval,
          maxReconnectAttempts: defaultConfig.maxReconnectAttempts,
          pingInterval: defaultConfig.heartbeatInterval
        }
      );
    } else {
      console.log('[CCBarService] 跳过WebSocket服务创建 - 使用轮询模式');
    }

    // 创建连接管理器
    this.connectionManager = new ConnectionManager(
      requestService,
      wsService,
      this.eventManager,
      defaultConfig
    );

    // 创建状态管理器
    this.stateManager = new StateManager(
      requestService,
      this.eventManager,
      this.globalStateManager
    );

    // 创建呼叫管理器 - 确保eventManager不为空
    if (!this.eventManager) {
      throw new Error('[CCBarService] EventManager未正确初始化');
    }

    this.callManager = new CallManager(
      requestService,
      this.eventManager,
      '',
      ''
    );

    // 创建转接管理器
    this.transferManager = new TransferManager(
      requestService,
      this.eventManager,
      '',
    );

    // 创建班长操作管理器
    this.supervisorManager = new SupervisorManager(
      requestService,
      this.eventManager,
      ''
    );

    console.log('[CCBarService] 所有模块初始化完成');
  }

  /**
   * 设置全局状态管理器的事件监听
   */
  private setupGlobalStateEventListeners(): void {
    // 监听状态变更事件
    this.eventManager.on('agent:stateChanged', (data: any) => {
      this.globalStateManager.setState(data.state);
    });

    // 监听呼叫事件
    this.eventManager.on('call:incoming', (data: CallEvent) => {
      this.globalStateManager.updateCallInfo(data, true);
    });

    this.eventManager.on('call:established', (data: CallEvent) => {
      this.globalStateManager.updateCallInfo(data, true);
    });

    this.eventManager.on('call:ended', () => {
      this.globalStateManager.updateCallInfo(null, true);
    });
  }

  /**
   * 获取CCBarService单例实例
   * @param config 配置参数
   * @returns CCBarService实例
   */
  public static getInstance(config: CCBarConfig): CCBarService {
    // 确保配置被正确初始化
    const initializedConfig = initializeWithConfig(config);

    if (!CCBarService.instance) {
      console.log('[CCBarService] 创建实例，使用配置:', initializedConfig);
      CCBarService.instance = new CCBarService(initializedConfig);
    } else if (config) {
      // 更新实例的配置
      console.log('[CCBarService] 更新配置:', initializedConfig);
      CCBarService.instance.updateConfig(initializedConfig);
    }
    return CCBarService.instance;
  }

  /**
   * 更新配置
   * @param config 新的配置参数
   */
  public updateConfig(config: CCBarConfig): void {
    console.log('[CCBarService] 配置更新前:', this.config);

    // 合并配置
    this.config = {
      ...this.config,
      ...config
    };

    console.log('[CCBarService] 配置更新后:', this.config);

    // 更新请求服务和WebSocket服务的配置
    if (this.connectionManager) {
      // 通过事件通知更新配置
      this.eventManager.emit('system:configUpdated', {
        config: this.config,
        timestamp: Date.now()
      });
    }
  }

  /* === 连接管理功能 === */

  /**
   * 座席登录
   * @param params 登录参数
   * @returns 登录结果isLoggedIn
   */
  public async login(params: LoginParams): Promise<CCBarResponse> {
    ccbarDebugger('login');
    // 调用连接管理器登录
    const loginResult = await this.connectionManager.login(params);

    // 如果登录成功，将话机信息保存到GlobalStateManager中
    if (loginResult.state) {
      // 保存话机信息
      this.globalStateManager.setPhone(params.phone || '');

    }

    return loginResult;
  }

  /**
   * 座席登出
   * @returns 登出结果
   */
  public async logout(): Promise<CCBarResponse> {
    ccbarDebugger('logout');
    return this.connectionManager.logout();
  }

  /**
   * 检查是否已登录
   * @returns 是否已登录
   */
  public isLoggedIn(): boolean {
    return sessionStorage.getItem('isLogined') === 'true' ||
      this.connectionManager.isLoggedIn();
  }

  /**
   * 获取登录状态信息
   * @returns 登录状态详情
   */
  public getLoginStatus(): {
    isLoggedIn: boolean;
    agentId: string;
    sessionId: string;
  } {
    const agentInfo = this.connectionManager.getAgentInfo();
    return {
      isLoggedIn: this.connectionManager.isLoggedIn(),
      agentId: agentInfo.agentId || '',
      sessionId: this.connectionManager.getSessionId()
    };
  }

  /* === 状态管理功能 === */

  /**
   * 座席置闲
   * @returns 操作结果
   */
  public async agentReady(): Promise<CCBarResponse> {
    ccbarDebugger('agentReady');
    return this.stateManager.setAgentReady();
  }

  /**
   * 座席置忙
   * @param busyType 忙碌类型
   * @returns 操作结果
   */
  public async agentNotReady(busyType?: string): Promise<CCBarResponse> {
    ccbarDebugger('agentNotReady');
    return this.stateManager.setAgentNotReady(busyType);
  }

  /**
   * 设置话后整理状态
   * @returns 操作结果
   */
  public async workNotReady(): Promise<CCBarResponse> {
    ccbarDebugger('workNotReady');
    return this.stateManager.setWorkNotReady();
  }

  /**
   * 完成话后整理
   * @returns 操作结果
   */
  public async workReady(): Promise<CCBarResponse> {
    ccbarDebugger('workReady');
    return this.stateManager.setWorkReady();
  }

  /**
   * 设置工作模式
   * @param mode 工作模式
   * @returns 操作结果
   */
  public async setWorkMode(mode: WorkModeType): Promise<CCBarResponse> {
    ccbarDebugger('setWorkMode');

    // 更新全局状态的工作模式
    this.globalStateManager.updateWorkMode(mode);
    return this.stateManager.setWorkMode(mode);
  }

  /**
   * 获取功能状态
   * @param func 功能名称
   * @returns 功能是否可用
   */
  public getFuncMask(func: string): boolean {
    // 优先使用全局状态管理器
    return this.globalStateManager.isFuncEnabled(func as FuncMaskType);
  }

  /**
   * 获取座席状态
   * @returns 座席状态
   */
  public getState(): string {
    // 使用全局状态管理器
    return this.globalStateManager.getStateType();
  }

  /**
   * 获取座席信息
   * @returns 座席信息
   */
  public getAgentInfo(): any {
    // 使用全局状态管理器和连接管理器结合的信息
    const agentInfo = this.connectionManager.getAgentInfo();
    const globalState = this.globalStateManager.getState();
    const phone = this.globalStateManager.getPhone();

    return {
      ...agentInfo,
      state: globalState.state,
      stateDesc: globalState.stateDesc,
      funcMask: globalState.funcMask,
      phone: phone || agentInfo.phone || '' // 确保包含话机信息
    };
  }

  /**
   * 获取完整的座席状态
   * @returns 完整的座席状态对象
   */
  public getAgentState(): AgentState {
    // 直接返回全局状态管理器的状态
    return this.globalStateManager.getState();
  }

  /* === 呼叫管理功能 === */

  /**
   * 拨打电话
   * @param phoneNumber 电话号码
   * @param displayNumber 外显号码
   * @param userData 用户数据
   * @param callType 呼叫类型
   * @returns 操作结果
   */
  public async makeCall(
    phoneNumber: string,
    displayNumber?: string,
    userData: any = {},
    callType: number = 2
  ): Promise<CCBarResponse> {
    ccbarDebugger('makeCall', { phoneNumber, displayNumber, userData, callType });
    return this.callManager.makeCall(phoneNumber, displayNumber, userData, callType);
  }

  /**
   * 应答呼叫
   * @returns 操作结果
   */
  public async answerCall(): Promise<CCBarResponse> {
    ccbarDebugger('answerCall');
    return this.callManager.answerCall();
  }

  /**
   * 挂断呼叫
   * @returns 操作结果
   */
  public async clearCall(callData?: any): Promise<CCBarResponse> {
    ccbarDebugger('clearCall');
    return this.callManager.hangupCall(callData);
  }

  /**
   * 保持呼叫
   * @returns 操作结果
   */
  public async holdCall(): Promise<CCBarResponse> {
    ccbarDebugger('holdCall');
    return this.callManager.holdCall();
  }

  /**
   * 取回呼叫
   * @returns 操作结果
   */
  public async unholdCall(): Promise<CCBarResponse> {
    ccbarDebugger('unholdCall');
    return this.callManager.retrieveCall();
  }

  /**
   * 静音呼叫
   * @returns 操作结果
   */
  public async muteCall(): Promise<CCBarResponse> {
    ccbarDebugger('muteCall');
    if (typeof this.callManager.muteCall === 'function') {
      return this.callManager.muteCall();
    }
    return {
      state: false,
      msg: '操作不支持',
      data: {
        code: 'fail',
        content: '当前呼叫管理器不支持静音操作'
      }
    };
  }

  /**
   * 取消静音呼叫
   * @returns 操作结果
   */
  public async unmuteCall(): Promise<CCBarResponse> {
    ccbarDebugger('unmuteCall');
    if (typeof this.callManager.unmuteCall === 'function') {
      return this.callManager.unmuteCall();
    }
    return {
      state: false,
      msg: '操作不支持',
      data: {
        code: 'fail',
        content: '当前呼叫管理器不支持取消静音操作'
      }
    };
  }

  /* === 转接管理功能 === */

  /**
   * 转接通话
   * @param params 转接参数
   * @returns 操作结果
   */
  public async transferCall(params: TransferCallParams): Promise<CCBarResponse> {
    ccbarDebugger('transferCall');
    return this.transferManager.transferCall(params);
  }

  /**
   * 完成转接
   * @returns 操作结果
   */
  public async completeTransfer(): Promise<CCBarResponse> {
    ccbarDebugger('completeTransfer');
    return this.transferManager.completeTransfer();
  }

  /**
   * 取消转接
   * @returns 操作结果
   */
  public async cancelTransfer(): Promise<CCBarResponse> {
    ccbarDebugger('cancelTransfer');
    return this.transferManager.cancelTransfer();
  }

  /* === 事件管理功能 === */

  /**
   * 订阅事件
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  public on(eventName: string, callback: Function): void {
    this.eventManager.on(eventName, callback);
  }

  /**
   * 取消订阅事件
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  public off(eventName: string, callback: Function): void {
    this.eventManager.off(eventName, callback);
  }

  /**
   * 一次性订阅事件
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  public once(eventName: string, callback: Function): void {
    this.eventManager.once(eventName, callback);
  }

  /**
   * 触发事件
   * @param eventName 事件名称
   * @param data 事件数据
   */
  public emit(eventName: string, data?: any): void {
    this.eventManager.emit(eventName, data);
  }

  /**
   * 停止轮询服务
   * 一般在会话超时或出现严重错误时调用
   */
  public stopPolling(): void {
    ccbarDebugger('stopPolling 被调用，尝试停止轮询服务');

    try {
      // 通过ConnectionManager停止轮询
      if (this.connectionManager) {
        // 直接访问ConnectionManager实例的私有方法可能会导致错误
        // 以下是一种变通方法，使用事件来通知需要停止轮询
        this.eventManager.emit('system:stopPolling', {
          reason: 'Manually stopped due to error',
          timestamp: new Date().getTime()
        });

        ccbarDebugger('已触发停止轮询事件');
      }
    } catch (error) {
      ccbarDebugger('停止轮询失败', error, 'error');
    }
  }

  /**
   * 初始化系统
   * @returns 初始化结果
   */
  public async init(): Promise<CCBarResponse> {
    ccbarDebugger('init');

    try {
      // 执行初始化逻辑，如检查系统状态、准备资源等
      return this.connectionManager.initCCbar();
    } catch (error: any) {
      ccbarDebugger('初始化失败', error, 'error');
      return {
        state: false,
        msg: error.message || '初始化失败',
        data: {
          code: '-1',
          content: error.message || '初始化失败'
        }
      };
    }
  }

  /**
   * 更新会话信息
   * @param sessionId 会话ID
   * @param agentId 座席ID
   */
  public updateSessionInfo(sessionId: string, agentId: string): void {
    ccbarDebugger('updateSessionInfo', { sessionId, agentId });

    // 更新各管理器中的会话ID
    if (this.callManager && typeof this.callManager.updateSessionId === 'function') {
      this.callManager.updateSessionId(sessionId);
    }

    if (this.transferManager && typeof this.transferManager.updateSessionId === 'function') {
      this.transferManager.updateSessionId(sessionId);
    }

    // SupervisorManager不再需要sessionId
    // 但可能需要更新agentId
    if (this.supervisorManager && agentId) {
      // 班长操作管理器的更新逻辑
      // 注意：已经实现了不使用sessionId，这里为了兼容性保留API调用
      this.supervisorManager.updateSessionId('');
    }
  }

  /**
   * 获取班长操作管理器
   * @returns 班长操作管理器实例
   */
  public get supervisor(): ISupervisorManager {
    return this.supervisorManager;
  }

  /**
   * 调用测试方法
   */
  public test(params: any = {}): any {
    console.log('[测试]', params);
    return {
      result: true,
      message: 'Test successful',
      data: params
    };
  }

  /**
   * 重置登录状态（不调用服务器接口）
   */
  public resetLoginState(): void {
    console.log('[CCBarService] 重置登录状态');

    try {
      // 重置全局状态
      this.globalStateManager.resetState();

      // 停止轮询服务
      if (this.pollingService) {
        this.pollingService.stopPolling();
        this.pollingService.setLoginState(false);
        this.pollingService.setSessionId(null);
        this.pollingService.setAgentId(null);
      }

      // 重置连接模块的状态
      if (this.connectionManager) {
        this.connectionManager.setLoginState(false);
      }

      // 清除会话信息
      this.sessionId = '';
      this.isLogined = false;

      // 强制清除本地存储的会话信息
      localStorage.removeItem('sessionId');
      localStorage.removeItem('agentId');
      localStorage.removeItem('cuid');
      sessionStorage.setItem('isLogined', 'false'); // 确保sessionStorage更新

      // 触发登出事件
      this.eventManager.emit('agent:forceLogout', {
        type: 'forceLogout',
        reason: '会话状态已重置',
        timestamp: Date.now()
      });

      console.log('[CCBarService] 登录状态重置完成');
    } catch (error) {
      console.error('[CCBarService] 重置登录状态失败:', error);
    }
  }
}

// 导出单例类
// export { CCBarService }; 



