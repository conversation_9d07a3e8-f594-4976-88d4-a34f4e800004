import { EventEmitter } from '../core/EventEmitter';

/**
 * WebSocket服务类
 * 负责管理WebSocket连接、重连逻辑、心跳机制和消息处理
 */
export class WebSocketService {
  private ws: WebSocket | null = null;
  private url: string;
  private reconnectInterval: number;
  private reconnectAttempts: number;
  private maxReconnectAttempts: number;
  private eventEmitter: EventEmitter;
  private _isConnected: boolean = false;
  private pingInterval: number | null = null;
  private pingTimer: ReturnType<typeof setInterval> | null = null;
  private sessionId: string = '';
  private _isHeartbeatActive: boolean = false;
  
  constructor(url: string, options: {
    reconnectInterval?: number;
    maxReconnectAttempts?: number;
    pingInterval?: number;
  } = {}) {
    this.url = url;
    this.reconnectInterval = options.reconnectInterval || 3000;
    this.maxReconnectAttempts = options.maxReconnectAttempts || 5;
    this.reconnectAttempts = 0;
    this.pingInterval = options.pingInterval || null;
    this.eventEmitter = EventEmitter.getInstance();
  }
  
  /**
   * 获取连接状态
   * @returns 是否已连接
   */
  public get isConnected(): boolean {
    return this._isConnected;
  }
  
  /**
   * 获取心跳状态
   * @returns 是否正在心跳
   */
  public get isHeartbeatActive(): boolean {
    return this._isHeartbeatActive;
  }
  
  /**
   * 连接WebSocket
   * @param queryString 连接参数，例如 "?sessionId=xxx&agentId=yyy"
   */
  public connect(queryString: string = ''): void {
    if (this.ws) {
      this.close();
    }
    
    try {
      // 提取查询字符串中的会话ID
      if (queryString && queryString.includes('sessionId=')) {
        const sessionIdMatch = queryString.match(/sessionId=([^&]+)/);
        if (sessionIdMatch && sessionIdMatch[1]) {
          this.sessionId = sessionIdMatch[1];
        }
      }
      
      const fullUrl = `${this.url}${queryString}`;
      this.ws = new WebSocket(fullUrl);
      
      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
      this.ws.onerror = this.handleError.bind(this);
    } catch (error) {
      this.eventEmitter.emit('error', error);
      this.reconnect();
    }
  }
  
  /**
   * 关闭WebSocket连接
   */
  public close(): void {
    if (this.ws) {
      this.ws.onopen = null;
      this.ws.onmessage = null;
      this.ws.onclose = null;
      this.ws.onerror = null;
      
      this.ws.close();
      this.ws = null;
      this._isConnected = false;
      
      if (this.pingTimer) {
        clearInterval(this.pingTimer);
        this.pingTimer = null;
      }
    }
  }
  
  /**
   * 发送消息
   * @param data 要发送的数据
   */
  public send(data: string | ArrayBufferLike | Blob | ArrayBufferView): boolean {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      this.eventEmitter.emit('error', new Error('WebSocket未连接'));
      return false;
    }
    
    try {
      this.ws.send(data);
      return true;
    } catch (error) {
      this.eventEmitter.emit('error', error);
      return false;
    }
  }
  
  /**
   * 添加事件监听
   * @param event 事件名称
   * @param callback 回调函数
   */
  public on(event: string, callback: Function): void {
    this.eventEmitter.on(event, callback);
  }
  
  /**
   * 移除事件监听
   * @param event 事件名称
   * @param callback 回调函数
   */
  public off(event: string, callback: Function): void {
    this.eventEmitter.off(event, callback);
  }
  
  /**
   * 处理WebSocket连接打开事件
   */
  private handleOpen(event: Event): void {
    this._isConnected = true;
    this.reconnectAttempts = 0;
    this.eventEmitter.emit('open', event);
    
    // 启动心跳
    if (this.pingInterval && !this.pingTimer) {
      this.pingTimer = setInterval(() => {
        this.ping();
      }, this.pingInterval);
    }
  }
  
  /**
   * 处理WebSocket接收消息事件
   */
  private handleMessage(event: MessageEvent): void {
    try {
      // 根据实际消息格式解析和处理数据
      const data = JSON.parse(event.data);
      this.eventEmitter.emit('message', data);
    } catch (error) {
      this.eventEmitter.emit('message', event.data);
    }
  }
  
  /**
   * 处理WebSocket连接关闭事件
   */
  private handleClose(event: CloseEvent): void {
    this._isConnected = false;
    this.eventEmitter.emit('close', event);
    
    if (this.pingTimer) {
      clearInterval(this.pingTimer);
      this.pingTimer = null;
    }
    
    this.reconnect();
  }
  
  /**
   * 处理WebSocket连接错误事件
   */
  private handleError(event: Event): void {
    this.eventEmitter.emit('error', event);
  }
  
  /**
   * 重新连接WebSocket
   */
  private reconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      
      setTimeout(() => {
        this.eventEmitter.emit('reconnecting', this.reconnectAttempts);
        this.connect(this.sessionId ? `?sessionId=${this.sessionId}` : '');
      }, this.reconnectInterval);
    } else {
      this.eventEmitter.emit('reconnect_failed');
    }
  }
  
  /**
   * 发送心跳包
   */
  private ping(): void {
    if (this._isConnected) {
      try {
        // 适配原始ccbar.js的心跳包格式
        const heartbeatData = {
          cmd: 'heartbeat',
          sessionId: this.sessionId,
          timestamp: new Date().getTime()
        };
        this.send(JSON.stringify(heartbeatData));
      } catch (error) {
        this.eventEmitter.emit('error', error);
      }
    }
  }
  
  /**
   * 启动心跳
   * @param interval 心跳间隔时间（毫秒），默认30000毫秒（30秒）
   */
  public startHeartbeat(interval: number = 30000): void {
    if (this._isHeartbeatActive) {
      return;
    }
    
    this._isHeartbeatActive = true;
    
    if (this.pingTimer) {
      clearInterval(this.pingTimer);
    }
    
    this.pingInterval = interval;
    this.pingTimer = setInterval(() => {
      this.ping();
    }, this.pingInterval);
    
    console.log(`WebSocket心跳已启动，间隔${interval}ms`);
  }
  
  /**
   * 停止心跳
   */
  public stopHeartbeat(): void {
    this._isHeartbeatActive = false;
    
    if (this.pingTimer) {
      clearInterval(this.pingTimer);
      this.pingTimer = null;
    }
    
    console.log('WebSocket心跳已停止');
  }
} 