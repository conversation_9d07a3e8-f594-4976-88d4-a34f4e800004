<!--
LoginDemo.vue - CCBar签入演示组件

此组件演示如何使用CCBarService模块化架构进行CCBar服务的签入、状态管理和呼叫控制。

使用方法：
1. 通过全局配置对象提供API地址和WebSocket地址：
   ```javascript
   // 在挂载组件前设置
   window.ccbarConfig = {
     apiBaseUrl: '/api',  // 使用Vite代理路径解决CORS问题
     wsUrl: '/ws'         // 使用Vite代理路径解决WebSocket连接问题
   };
   ```

2. 也可以通过环境变量设置(如果项目支持)：
   在.env.development或.env.production中：
   ```
   VITE_CCB_API_URL=/api
   VITE_CCB_WS_URL=/ws
   ```

3. 如果以上都未设置，将使用默认代理路径：
   - API地址: /api (代理到 http://*************:9060)
   - WebSocket地址: /ws (代理到 ws://*************:9060/ws)

4. CORS问题解决方案：
   该组件使用Vite的代理配置解决跨域(CORS)问题。请确保vite.config.ts中已正确配置代理：
   ```javascript
   proxy: {
     '/api': {
       target: 'http://*************:9060',
       changeOrigin: true,
       rewrite: (path) => path.replace(/^\/api/, '')
     },
     '/ws': {
       target: 'ws://*************:9060',
       ws: true,
       changeOrigin: true
     }
   }
   ```

5. 模块化架构：
   该组件使用CCBarService模块化架构，而不是直接使用HTTP请求。
   这种方式提供了更好的代码组织和复用能力，同时保持了与原始ccbar.js相同的功能。
-->

<template>
  <div class="login-demo">
    <h1>CCBar 签入演示</h1>

    <div class="card">
      <div class="card-header">
        <span>签入配置</span>
      </div>
      <div class="card-body">
        <el-form :model="loginForm" label-width="100px">
          <el-form-item label="账号">
            <el-input
              v-model="loginForm.agentId"
              placeholder="请输入账号"
            ></el-input>
          </el-form-item>
          <el-form-item label="密码">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
            ></el-input>
          </el-form-item>
          <el-form-item label="话机号">
            <el-input
              v-model="loginForm.phone"
              placeholder="请输入话机号"
            ></el-input>
          </el-form-item>
          <el-form-item label="企业ID">
            <el-input
              v-model="loginForm.entId"
              placeholder="请输入企业ID"
            ></el-input>
          </el-form-item>
          <el-form-item label="登录密钥">
            <el-input
              v-model="loginForm.loginKey"
              placeholder="请输入登录密钥（32位）"
            ></el-input>
          </el-form-item>
          <el-form-item label="产品ID">
            <el-input
              v-model="loginForm.productId"
              placeholder="请输入产品ID"
            ></el-input>
          </el-form-item>
          <el-form-item label="工作模式">
            <el-select
              v-model="loginForm.workMode"
              placeholder="请选择工作模式"
            >
              <el-option label="预览外呼" value="preview"></el-option>
              <el-option label="自动外呼" value="auto"></el-option>
              <el-option label="全部" value="all"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="就绪模式">
            <el-select
              v-model="loginForm.readyMode"
              placeholder="请选择就绪模式"
            >
              <el-option label="自动就绪" value="autoReady"></el-option>
              <el-option label="手动就绪" value="notReady"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="自动应答">
            <el-switch v-model="loginForm.autoAnswer"></el-switch>
          </el-form-item>
          <el-form-item label="强制登录">
            <el-switch v-model="loginForm.force"></el-switch>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              @click="handleLogin"
              >签入</el-button
            >
            <el-button @click="handleLogout" 
              >签出</el-button
            >
          </el-form-item>
        </el-form>
      </div>
    </div>

    <div class="card">
      <div class="card-header">
        <span>状态和操作</span>
      </div>
      <div class="card-body">
        <div class="status-info">
          <div class="status-item">
            <span class="status-label">登录状态:</span>
            <span
              class="status-value"
              :class="{
                'status-connected': isLoggedIn,
                'status-disconnected': !isLoggedIn,
              }"
            >
              {{ isLoggedIn ? "已登录" : "未登录" }}
            </span>
          </div>
          <div class="status-item">
            <span class="status-label">座席状态:</span>
            <span
              class="status-value"
              :class="getAgentStatusClass(agentState.state)"
            >
              {{ agentState.stateDesc }}
            </span>
          </div>
          <div class="status-item">
            <span class="status-label">座席ID:</span>
            <span class="status-value">{{ agentId || "无" }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">话机号码:</span>
            <span class="status-value">{{ loginForm.phone || "无" }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">会话ID:</span>
            <span class="status-value">{{ sessionId || "无" }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">连接状态:</span>
            <span
              class="status-value"
              :class="{
                'status-connected': wsConnected,
                'status-disconnected': !wsConnected,
              }"
            >
              {{ wsConnected ? "WebSocket已连接" : "WebSocket未连接" }}
            </span>
          </div>
        </div>

        <div class="action-buttons">
          <el-button-group>
            <el-button
              type="primary"
              @click="setAgentReady"
              :disabled="!isLoggedIn || !canSetReady"
            >
              置闲
            </el-button>
            <el-button
              type="warning"
              @click="setAgentNotReady"
              :disabled="!isLoggedIn || !canSetNotReady"
            >
              置忙
            </el-button>
          </el-button-group>

          <div class="status-actions">
            <el-divider content-position="center">状态管理</el-divider>
            <el-row :gutter="10">
              <el-col :span="8">
                <el-button
                  size="small"
                  @click="setAgentState('break')"
                  :disabled="!isLoggedIn"
                >
                  休息中
                </el-button>
              </el-col>
              <el-col :span="8">
                <el-button
                  size="small"
                  @click="setAgentState('meeting')"
                  :disabled="!isLoggedIn"
                >
                  会议中
                </el-button>
              </el-col>
              <el-col :span="8">
                <el-button
                  size="small"
                  @click="setAgentState('training')"
                  :disabled="!isLoggedIn"
                >
                  培训中
                </el-button>
              </el-col>
            </el-row>
          </div>

          <div class="call-actions">
            <el-divider content-position="center">呼叫控制</el-divider>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-input
                  v-model="phoneNumber"
                  placeholder="请输入电话号码"
                ></el-input>
              </el-col>
              <el-col :span="12">
                <el-button
                  type="success"
                  @click="makeCall"
                  :disabled="
                    !isLoggedIn ||
                    !phoneNumber ||
                    !canMakeCall
                  "
                >
                  拨打电话
                </el-button>
              </el-col>
            </el-row>
            <el-row :gutter="10" class="call-control-buttons">
              <el-col :span="8">
                <el-button
                  size="small"
                  type="danger"
                  @click="hangupCall"
                  :disabled="
                    !isLoggedIn ||
                    !canHangupCall
                  "
                >
                  挂断
                </el-button>
              </el-col>
              <el-col :span="8">
                <el-button
                  size="small"
                  @click="holdCall"
                  :disabled="!isLoggedIn || !canHoldCall"
                >
                  保持
                </el-button>
              </el-col>
              <el-col :span="8">
                <el-button
                  size="small"
                  @click="retrieveCall"
                  :disabled="!isLoggedIn || !canRetrieveCall"
                >
                  恢复
                </el-button>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </div>

    <div class="card">
      <div class="card-header">
        <span>事件日志</span>
        <el-button size="small" @click="clearEvents">清空</el-button>
      </div>
      <div class="card-body event-log">
        <div v-if="events.length === 0" class="no-events">暂无事件</div>
        <div v-else class="event-list">
          <div v-for="(event, index) in events" :key="index" class="event-item">
            <div class="event-time">{{ event.time }}</div>
            <div class="event-type" :class="`event-type-${event.type}`">
              {{ event.type }}
            </div>
            <div class="event-content">{{ event.content }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加原始事件数据显示区 -->
    <div class="card" v-if="showDebug">
      <div class="card-header">
        <span>调试面板</span>
        <el-switch v-model="showDebug" active-text="显示" inactive-text="隐藏"></el-switch>
      </div>
      <div class="card-body">
        <h3>本地状态对象:</h3>
        <pre class="debug-data">{{ JSON.stringify(agentState, null, 2) }}</pre>
        
        <h3>最后一次收到的agentStateSync事件:</h3>
        <pre class="debug-data" v-if="lastAgentStateSync">{{ JSON.stringify(lastAgentStateSync, null, 2) }}</pre>
        <div v-else>暂无数据</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onBeforeUnmount, watch } from "vue";
import { ElMessage } from "element-plus";
import type { CCBarWithService, AgentState } from "../types";
import { AgentStateType } from "../types";
import { CCBarService } from "../core/CCBarService";
import { ConnectionManager } from "../core/modules/ConnectionManager";
import { RequestService } from "../utils/request";
import { WebSocketService } from "../utils/websocket";
import { EventEmitter } from "../core/EventEmitter";
import { EventManager } from "../core/modules/EventManager";
import { LoginParams, CCBarConfig } from "../types";
import { loadConfig } from "../utils/config";
import GlobalStateManager from "../core/GlobalStateManager";

// 全局CCBar对象
declare global {
  interface Window {
    CCBar: CCBarWithService;
    // 单独存储模块实例，避免修改CCBarWithService类型
    ccbarModules?: {
      connectionManager?: ConnectionManager;
      eventManager?: EventManager;
      requestService?: RequestService;
      wsService?: WebSocketService;
    };
  }
}

// 确保window.CCBar存在
if (!window.CCBar) {
  window.CCBar = {} as CCBarWithService;
}

// 确保window.ccbarModules存在
if (!window.ccbarModules) {
  window.ccbarModules = {};
}

// 登录表单
const loginForm = reactive({
  agentId: "",
  password: "",
  phone: "55552013",
  entId: "",
  loginKey: "",
  productId: "",
  workMode: "preview",
  readyMode: "notReady",
  autoAnswer: false,
  force: false,
});

// 状态变量
const loading = ref(false);
const wsConnected = ref(false);
const events = ref<Array<{ time: string; type: string; content: string }>>([]);
const stateIntervalId = ref<number | null>(null); // 用ref管理定时器ID

// 获取全局状态管理器实例
const globalStateManager = GlobalStateManager.getInstance();

// 声明一个直接的ref来跟踪登录状态
const loggedInState = ref(globalStateManager.isLoggedIn());

// 获取全局状态
const isLoggedIn = computed(() => loggedInState.value);

// 从全局状态管理器获取状态
const agentState = reactive({
  state: "",
  stateDesc: "未登录",
  funcMask: {} as Record<string, boolean>
});
const agentId = computed(() => globalStateManager.getAgentId());
const sessionId = computed(() => globalStateManager.getSessionId());

// 加载配置工具中的配置
const appConfig = loadConfig();

// 调试变量
const showDebug = ref(true);
const lastAgentStateSync = ref<any>(null);

// 初始化表单中的企业相关配置
onMounted(() => {
  loginForm.entId = appConfig.entId || "";
  loginForm.loginKey = appConfig.loginKey || "";
  loginForm.productId = appConfig.productId || "";

  console.log("LoginDemo组件已挂载");

  // 设置全局状态监听
  setupGlobalStateListeners();

  // 输出配置信息
  console.log("当前配置:");
  console.log("- API地址:", appConfig.baseURL, "(使用Vite代理解决CORS问题)");
  console.log(
    "- WebSocket地址:",
    appConfig.wsURL,
    "(使用Vite代理解决WebSocket连接问题)"
  );
  addEvent(
    "初始化",
    `CCBar签入演示已初始化，API地址: ${appConfig.baseURL} (通过代理访问)`
  );

  // 检查window.ccbarConfig
  if (window.ccbarConfig) {
    console.log("全局配置对象:");
    console.log("- apiBaseUrl:", window.ccbarConfig.apiBaseUrl || "未设置");
    console.log("- wsUrl:", window.ccbarConfig.wsUrl || "未设置");
  } else {
    console.log("未找到全局配置对象 window.ccbarConfig");
    console.log("使用默认代理路径解决CORS问题");
  }

  // 设置事件处理器
  setupEventHandlers();
});

// 组件卸载时清理事件监听
onBeforeUnmount(() => {
  console.log("LoginDemo组件将卸载，清理事件监听");
  
  // 清理定时器
  if (stateIntervalId.value) {
    clearInterval(stateIntervalId.value);
    stateIntervalId.value = null;
  }
  
  // 简化清理逻辑，避免复杂的错误处理
  console.log("将清理事件监听，如有遗留请在控制台检查");
  addEvent("清理", "组件即将卸载");
});

// 初始化CCBar服务实例
const ccbarConfig: CCBarConfig = {
  ...appConfig,
};

// 获取CCBarService实例
const ccbarService = CCBarService.getInstance(ccbarConfig);

// 输出当前使用的配置，方便调试
console.log("当前配置:", ccbarConfig);

// 创建必要的服务实例
const requestService = new RequestService(
  10000,
  true
);
const wsService = new WebSocketService(ccbarConfig.wsURL || "/ws");
const eventEmitter = EventEmitter.getInstance();
const eventManager = new EventManager(eventEmitter);

// 创建ConnectionManager实例
const connectionManager = new ConnectionManager(
  requestService,
  wsService,
  eventManager,
  ccbarConfig
);

// 呼叫相关
const phoneNumber = ref("");
const activeCallId = ref("");

// 添加用于控制按钮状态的计算属性
const canMakeCall = computed(() => agentState.funcMask?.makecall || false);
const canSetReady = computed(() => agentState.funcMask?.agentready || false);
const canSetNotReady = computed(() => agentState.funcMask?.agentnotready || false);
const canHangupCall = computed(() => agentState.funcMask?.clearcall || false);
const canHoldCall = computed(() => agentState.funcMask?.holdcall || false);
const canRetrieveCall = computed(() => agentState.funcMask?.unholdcall || false);
const canLogout = computed(() => agentState.funcMask?.logoff || false);

// 状态监听函数
const setupGlobalStateListeners = () => {
  // 监听状态变更
  globalStateManager.onStateChanged((event) => {
    console.log("收到GlobalStateManager状态变更事件:", event);
    
    // 获取状态数据，根据不同的数据结构进行适配
    const stateData = (event.currentState && event.currentState.cmddata) 
      ? event.currentState.cmddata 
      : (event.currentState || event);
    
    console.log("提取的状态数据:", stateData);
    
    // 添加防御性检查，确保stateData存在
    if (stateData) {
      // 更新本地状态对象
      if (stateData.state) {
        agentState.state = stateData.state;
        console.log(`状态已更新: ${stateData.state}`);
      }
      
      if (stateData.stateDesc) {
        agentState.stateDesc = stateData.stateDesc;
        console.log(`状态描述已更新: ${stateData.stateDesc}`);
      }
      
      if (stateData.funcMask) {
        // 使用深拷贝更新功能掩码，避免引用问题
        agentState.funcMask = {...stateData.funcMask};
        
        // 记录可用功能
        const availableFunctions = Object.entries(stateData.funcMask)
          .filter(([_, value]) => value)
          .map(([key]) => key)
          .join(', ');
        
        console.log("功能掩码已更新:", availableFunctions);
      }
      
      // 更新登录状态
      loggedInState.value = globalStateManager.isLoggedIn();
      
      addEvent("状态变更", `座席状态变更为: ${stateData.stateDesc || agentState.stateDesc || '未知状态'}`);
      console.log("GlobalStateManager更新后的本地状态:", {
        state: agentState.state,
        stateDesc: agentState.stateDesc,
        funcMaskKeys: Object.keys(agentState.funcMask || {})
      });
    } else {
      addEvent("状态变更", "收到状态变更事件，但状态信息不完整");
      console.warn("收到不完整的状态变更事件:", event);
    }
  });

  // 监听登录状态变更
  globalStateManager.onError((event) => {
    // 添加防御性检查
    if (event && event.error) {
      addEvent("错误", event.error);
      ElMessage.error(event.error);
    } else {
      addEvent("错误", "发生未知错误");
      console.warn("收到不完整的错误事件:", event);
    }
  });
};

// 获取状态样式
const getAgentStatusClass = (status: string) => {
  switch (status) {
    case AgentStateType.IDLE:
      return "status-ready";
    case AgentStateType.BUSY:
      return "status-not-ready";
    case AgentStateType.TALK:
    case AgentStateType.ALERTING:
    case AgentStateType.HELD:
      return "status-in-call";
    case AgentStateType.WORKNOTREADY:
      return "status-after-call";
    case AgentStateType.LOGOFF:
      return "status-disconnected";
    default:
      return "";
  }
};

// 添加事件日志
const addEvent = (type: string, content: string, id: string = '') => {
  const now = new Date();
  const time = `${now.getHours().toString().padStart(2, "0")}:${now
    .getMinutes()
    .toString()
    .padStart(2, "0")}:${now.getSeconds().toString().padStart(2, "0")}`;

  // 构建事件内容，如果提供了ID则添加
  const eventContent = id ? `[ID:${id}] ${content}` : content;
  
  events.value.unshift({
    time,
    type,
    content: eventContent,
  });

  // 限制最多显示50条
  if (events.value.length > 50) {
    events.value.pop();
  }
};

// 清空事件日志
const clearEvents = () => {
  events.value = [];
};

// 修改事件处理相关代码
const setupEventHandlers = () => {
  // 设置一个定时器，定期检查登录状态
  const intervalId = setInterval(() => {
    // 检查登录状态，通过agentState.state判断
    const isGlobalLoggedIn = globalStateManager.isLoggedIn();
    if (loggedInState.value !== isGlobalLoggedIn) {
      console.log("检测到登录状态不同步，更新登录状态:", isGlobalLoggedIn);
      loggedInState.value = isGlobalLoggedIn;
      
      // 如果登录状态变为未登录，重置本地状态
      if (!isGlobalLoggedIn) {
        agentState.state = 'LOGOFF';
        agentState.stateDesc = '未登录';
        agentState.funcMask = {};
        console.log("已重置本地状态");
      }
    }
  }, 2000); // 每2秒检查一次
  
  // 保存定时器ID以便清理
  stateIntervalId.value = intervalId as unknown as number;
  
  // 监听通知事件
  eventManager.on('notify:respLogin', (event: any) => {
    const { resultCode, resultDesc } = event.cmddata;
    
    // 处理登录响应
    if (resultCode === '0' || resultDesc === '成功') {
      // 由GlobalStateManager更新登录状态，此处只更新本地显示
      if (event.cmddata.result) {
        const { workno, loginTime } = event.cmddata.result;
        addEvent('登录响应', `工号: ${workno}, 登录时间: ${new Date(parseInt(loginTime)).toLocaleTimeString()}`, 'LOGIN_SUCCESS');
      }
    } else {
      // 登录失败
      addEvent('登录响应', `登录失败: ${resultDesc}`, 'LOGIN_FAILED');
      ElMessage.error(`登录失败: ${resultDesc}`);
    }
  });
  
  // 监听登出响应
  eventManager.on('notify:respLogout', (event: any) => {
    const { resultCode, resultDesc } = event.cmddata;
    addEvent('登出响应', `登出结果: ${resultDesc}`, 'LOGOUT_RESPONSE');
    
    if (resultCode === '0' || resultDesc === '成功') {
      // 由GlobalStateManager更新状态，此处只更新本地显示
      agentState.state = 'LOGOFF';
      agentState.stateDesc = '未登录';
      agentState.funcMask = {};
    }
  });
  
  // 监听状态同步事件
  eventManager.on('agent:stateChanged', (data: any) => {
    console.log("收到agent:stateChanged状态变更事件:", data);
    
    // 从cmddata获取状态信息，如果不存在则使用data本身
    const stateData = data.cmddata || data;
    
    console.log("agent:stateChanged提取的状态数据:", stateData);
    
    // 确保stateData存在
    if (stateData) {
      // 更新状态描述
      if (stateData.state) {
        agentState.state = stateData.state;
        agentState.stateDesc = stateData.stateDesc || getStateDescription(stateData.state);
        
        console.log(`agent:stateChanged状态已更新: ${stateData.state}, 描述: ${agentState.stateDesc}`);
        addEvent('状态变更', `座席状态: ${agentState.stateDesc} (${stateData.state})`, 'STATE_CHANGED');
      } else {
        console.warn("收到的agent:stateChanged事件中没有state属性:", data);
      }
      
      // 更新功能掩码
      if (stateData.funcMask) {
        // 完全替换功能掩码，使用深拷贝
        agentState.funcMask = {...stateData.funcMask};
        
        // 记录可用功能
        const availableFunctions = Object.entries(stateData.funcMask)
          .filter(([_, value]) => value)
          .map(([key]) => key)
          .join(', ');
        
        console.log("agent:stateChanged更新功能掩码:", availableFunctions);
        addEvent('功能掩码', `可用功能: ${availableFunctions}`, 'FUNCMASK_UPDATED');
      }
      
      console.log("agent:stateChanged更新后的本地状态:", {
        state: agentState.state,
        stateDesc: agentState.stateDesc,
        funcMaskKeys: Object.keys(agentState.funcMask || {})
      });
    } else {
      console.warn("收到不完整的agent:stateChanged事件:", data);
      addEvent('状态变更', '收到状态变更事件，但状态信息不完整', 'STATE_CHANGED_INCOMPLETE');
    }
  });
  
  // 添加agentStateSync事件监听
  eventManager.on('agentStateSync', (data: any) => {
    console.log("收到agentStateSync事件:", data);
    
    // 保存最后一次接收的事件数据
    lastAgentStateSync.value = data;
    
    // 从cmddata或直接获取状态信息
    const stateData = data.cmddata || data;
    
    console.log("agentStateSync提取的状态数据:", stateData);
    
    // 确保stateData存在
    if (stateData) {
      // 更新状态描述
      if (stateData.state) {
        agentState.state = stateData.state;
        // 优先使用服务器返回的状态描述
        agentState.stateDesc = stateData.stateDesc || getStateDescription(stateData.state);
        
        // 调试输出
        console.log(`agentStateSync状态已更新: ${stateData.state}, 描述: ${agentState.stateDesc}`);
        addEvent('状态同步', `座席状态更新: ${agentState.stateDesc} (${stateData.state})`);
      } else {
        console.warn("收到的agentStateSync事件中没有state属性:", data);
      }
      
      // 更新功能掩码
      if (stateData.funcMask) {
        // 完全替换功能掩码，使用深拷贝
        agentState.funcMask = {...stateData.funcMask};
        
        // 记录可用功能
        const availableFunctions = Object.entries(stateData.funcMask)
          .filter(([_, value]) => value)
          .map(([key]) => key)
          .join(', ');
        
        console.log("agentStateSync更新功能掩码:", availableFunctions);
        addEvent('功能掩码', `可用功能: ${availableFunctions}`);
      }
      
      console.log("agentStateSync更新后的本地状态:", {
        state: agentState.state,
        stateDesc: agentState.stateDesc,
        funcMaskKeys: Object.keys(agentState.funcMask || {})
      });
    } else {
      console.warn("收到不完整的agentStateSync事件:", data);
      addEvent('状态同步', '收到状态同步事件，但状态信息不完整');
    }
  });
  
  // 监听来电事件
  eventManager.on('call:incoming', (data: any) => {
    const phoneNumber = data.phoneNumber || data.cmddata?.phoneNumber || '未知';
    const callerName = data.callerName || data.cmddata?.callerName || '未知';
    
    addEvent('来电', `来电号码: ${phoneNumber}, 来电名称: ${callerName}`);
    ElMessage.info(`收到来电: ${phoneNumber}`);
  });
  
  // 监听其他通知事件
  eventManager.on('polling:events', (events: any) => {
    // 防御性检查确保events是数组
    if (Array.isArray(events)) {
      addEvent('轮询', `收到${events.length}个事件: ${events.map(e => e.messageId || e.type).join(', ')}`);
    } else {
      // 如果不是数组，则以单个事件处理
      const eventType = events?.messageId || events?.type || '未知类型';
      addEvent('轮询', `收到轮询事件: ${eventType}`);
      console.log('轮询接收到非数组事件:', events);
    }
  });
};

// 获取状态描述
const getStateDescription = (state: string): string => {
  const stateMap: Record<string, string> = {
    'IDLE': '空闲',
    'READY': '就绪',
    'BUSY': '忙碌',
    'TALK': '通话中',
    'ALERTING': '振铃中',
    'HELD': '保持中',
    'OFFLINE': '离线',
    'ACW': '话后处理'
  };
  
  return stateMap[state] || state;
};

// 签入处理函数
const handleLogin = async () => {
  if (!loginForm.agentId || !loginForm.password) {
    ElMessage.error("请输入账号和密码");
    return;
  }

  try {
    loading.value = true;

    // 构建登录参数
    const loginParams: LoginParams = {
      username: loginForm.agentId,
      password: loginForm.password,
      phone: loginForm.phone,
      phoneType: "softphone",
      loginType: 0,
      readyMode: loginForm.readyMode,
      workMode: loginForm.workMode,
      autoAnswer: loginForm.autoAnswer,
      force: loginForm.force,
    };

    // 记录登录请求
    addEvent(
      "登录",
      `发送登录请求: 账号=${loginForm.agentId}, 话机=${loginForm.phone}`
    );

    // 直接使用CCBarService实例调用login方法
    const result = await ccbarService.login(loginParams);

    if (result.state) {
      wsConnected.value = true;
      addEvent("连接", `登录成功，WebSocket已连接`);
      ElMessage.success("签入成功");
      addEvent("登录", "签入成功");

      // 监听WebSocket事件
      wsService.on("message", (data: any) => {
        if (data.event) {
          addEvent("WebSocket", `收到事件: ${data.event}`);
        }
      });

      // 存储ConnectionManager和其他服务实例，以便后续操作使用
      if (window.ccbarModules) {
        window.ccbarModules.connectionManager = connectionManager;
        window.ccbarModules.eventManager = eventManager;
        window.ccbarModules.requestService = requestService;
        window.ccbarModules.wsService = wsService;
      }
    } else {
      ElMessage.error(`签入失败: ${result.msg || "未知错误"}`);
      addEvent("错误", `签入失败: ${result.msg || "未知错误"}`);
    }
  } catch (error: any) {
    console.error("签入失败:", error);
    ElMessage.error(`签入异常: ${error.message || "未知错误"}`);
    addEvent("错误", `签入异常: ${error.message || "未知错误"}`);
  } finally {
    loading.value = false;
  }
};

// 签出
const handleLogout = async () => {
  loading.value = true;

  try {
    // 使用CCBarService实例调用logout方法
    const result = await ccbarService.logout();

    if (result.state) {
      wsConnected.value = false;
      localStorage.removeItem("cuid");

      addEvent("登出", "签出成功");
      ElMessage.success("签出成功");

      // 清理保存的实例
      if (window.ccbarModules) {
        window.ccbarModules = {};
      }
    } else {
      ElMessage.error(`签出失败: ${result.msg || "未知错误"}`);
      addEvent("错误", `签出失败: ${result.msg || "未知错误"}`);
    }
  } catch (error: any) {
    console.error("签出失败:", error);
    addEvent("错误", `签出失败: ${error.message || "未知错误"}`);
    ElMessage.error(`签出失败: ${error.message || "未知错误"}`);
  } finally {
    loading.value = false;
  }
};

// 座席置闲
const setAgentReady = async () => {
  try {
    loading.value = true;
    addEvent("状态", "正在设置座席状态为空闲...");

    // 检查是否有权限
    if (!canSetReady.value) {
      ElMessage.warning('当前状态不允许进行此操作');
      addEvent("错误", "当前状态不允许设置为空闲");
      return;
    }

    // 直接使用CCBarService实例调用agentReady方法
    const result = await ccbarService.agentReady();

    if (result.state) {
      ElMessage.success("已置闲");
      addEvent("状态", "座席已置闲");
    } else {
      ElMessage.error(`置闲失败: ${result.msg || "未知错误"}`);
      addEvent("错误", `置闲失败: ${result.msg || "未知错误"}`);
    }
  } catch (error: any) {
    ElMessage.error(`置闲出错: ${error.message || "未知错误"}`);
    addEvent("错误", `置闲出错: ${error.message || "未知错误"}`);
  } finally {
    loading.value = false;
  }
};

// 座席置忙
const setAgentNotReady = async () => {
  try {
    loading.value = true;
    addEvent("状态", "正在设置座席状态为繁忙...");

    // 直接使用CCBarService实例调用agentNotReady方法
    const result = await ccbarService.agentNotReady();

    if (result.state) {
      ElMessage.success("已置忙");
      addEvent("状态", "座席已置忙");
    } else {
      ElMessage.error(`置忙失败: ${result.msg || "未知错误"}`);
      addEvent("错误", `置忙失败: ${result.msg || "未知错误"}`);
    }
  } catch (error: any) {
    ElMessage.error(`置忙出错: ${error.message || "未知错误"}`);
    addEvent("错误", `置忙出错: ${error.message || "未知错误"}`);
  } finally {
    loading.value = false;
  }
};

// 座席状态变更通用函数
const setAgentState = async (state: string) => {
  try {
    loading.value = true;

    // 根据ccbar.js规范，通过不同原因码设置座席状态
    let reasonCode;
    let reasonDesc;

    switch (state) {
      case "break":
        reasonCode = "1";
        reasonDesc = "休息中";
        break;
      case "meeting":
        reasonCode = "2";
        reasonDesc = "会议中";
        break;
      case "training":
        reasonCode = "3";
        reasonDesc = "培训中";
        break;
      default:
        reasonCode = "0";
        reasonDesc = "其他原因";
    }

    addEvent("状态", `正在设置座席状态为${reasonDesc}...`);

    // 直接使用CCBarService实例调用agentNotReady方法，带上忙碌类型
    const result = await ccbarService.agentNotReady(reasonCode);
    if (result.state) {
      ElMessage.success(`状态已更改为: ${reasonDesc}`);
      addEvent("状态变更", `座席状态变更为: ${reasonDesc}`);
    } else {
      ElMessage.error(`状态变更失败: ${result.msg || "未知错误"}`);
      addEvent("错误", `状态变更失败: ${result.msg || "未知错误"}`);
    }
  } catch (error: any) {
    ElMessage.error(`状态变更出错: ${error.message || "未知错误"}`);
    addEvent("错误", `状态变更出错: ${error.message || "未知错误"}`);
  } finally {
    loading.value = false;
  }
};

// 拨打电话
const makeCall = async () => {
  try {
    if (!phoneNumber.value) {
      ElMessage.warning("请输入电话号码");
      return;
    }

    loading.value = true;
    addEvent("呼叫", `开始拨号: ${phoneNumber.value}`);

    // 使用CCBarService实例调用makeCall方法
    const result = await ccbarService.makeCall(phoneNumber.value);

    if (result.state) {
      activeCallId.value = result.data?.result?.callId || "call_" + Date.now();
      ElMessage.success(`正在拨打电话: ${phoneNumber.value}`);
    } else {
      ElMessage.error(`拨号失败: ${result.msg || "未知错误"}`);
      addEvent("错误", `拨号失败: ${result.msg || "未知错误"}`);
    }
  } catch (error: any) {
    ElMessage.error(`拨号失败: ${error.message || "未知错误"}`);
    addEvent("错误", `拨号失败: ${error.message || "未知错误"}`);
  } finally {
    loading.value = false;
  }
};

// 挂断电话
const hangupCall = async () => {
  try {
    loading.value = true;
    addEvent("呼叫", "挂断电话");

    // 使用CCBarService实例调用clearCall方法
    const result = await ccbarService.clearCall();

    if (result.state) {
      activeCallId.value = "";
      ElMessage.success("通话已结束");
    } else {
      ElMessage.error(`挂断失败: ${result.msg || "未知错误"}`);
      addEvent("错误", `挂断失败: ${result.msg || "未知错误"}`);
    }
  } catch (error: any) {
    ElMessage.error(`挂断失败: ${error.message || "未知错误"}`);
    addEvent("错误", `挂断失败: ${error.message || "未知错误"}`);
  } finally {
    loading.value = false;
  }
};

// 保持通话
const holdCall = async () => {
  try {
    loading.value = true;
    addEvent("呼叫", "保持通话");

    // 使用CCBarService实例调用holdCall方法
    const result = await ccbarService.holdCall();
    if (result.state) {
      ElMessage.success("通话已保持");
    } else {
      ElMessage.error(`保持失败: ${result.msg || "未知错误"}`);
      addEvent("错误", `保持失败: ${result.msg || "未知错误"}`);
    }
  } catch (error: any) {
    ElMessage.error(`保持失败: ${error.message || "未知错误"}`);
    addEvent("错误", `保持失败: ${error.message || "未知错误"}`);
  } finally {
    loading.value = false;
  }
};

// 恢复通话
const retrieveCall = async () => {
  try {
    loading.value = true;
    addEvent("呼叫", "恢复通话");

    // 使用CCBarService实例调用unholdCall方法
    const result = await ccbarService.unholdCall();

    if (result.state) {
      ElMessage.success("通话已恢复");
    } else {
      ElMessage.error(`恢复失败: ${result.msg || "未知错误"}`);
      addEvent("错误", `恢复失败: ${result.msg || "未知错误"}`);
    }
  } catch (error: any) {
    ElMessage.error(`恢复失败: ${error.message || "未知错误"}`);
    addEvent("错误", `恢复失败: ${error.message || "未知错误"}`);
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.login-demo {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.login-demo h1 {
  font-size: 24px;
  margin-bottom: 20px;
  text-align: center;
}

.card {
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.card-header {
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  border-bottom: 1px solid #e6e6e6;
  background-color: #f5f7fa;
}

.card-header span {
  font-size: 16px;
  font-weight: bold;
}

.card-body {
  padding: 20px;
}

.status-info {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  margin-bottom: 20px;
}

.status-item {
  display: flex;
  align-items: center;
  padding: 5px 0;
}

.status-label {
  width: 100px;
  font-weight: bold;
}

.status-value {
  flex: 1;
}

.status-connected {
  color: #67c23a;
}

.status-disconnected {
  color: #f56c6c;
}

.status-ready {
  color: #67c23a;
}

.status-not-ready {
  color: #e6a23c;
}

.status-in-call {
  color: #409eff;
}

.status-after-call {
  color: #909399;
}

.action-buttons {
  margin-top: 20px;
}

.action-buttons .el-divider {
  margin: 15px 0;
}

.status-actions,
.call-actions {
  margin-top: 15px;
}

.call-control-buttons {
  margin-top: 10px;
}

.event-log {
  height: 300px;
  overflow-y: auto;
}

.no-events {
  text-align: center;
  color: #909399;
  padding: 20px 0;
}

.event-list {
  display: flex;
  flex-direction: column;
}

.event-item {
  display: flex;
  padding: 5px 0;
  border-bottom: 1px solid #f0f0f0;
}

.event-time {
  width: 80px;
  color: #909399;
  font-size: 12px;
}

.event-type {
  width: 80px;
  padding: 0 10px;
  color: #fff;
  font-size: 12px;
  text-align: center;
  border-radius: 10px;
  margin-right: 10px;
  line-height: 20px;
}

.event-type-登录,
.event-type-连接 {
  background-color: #67c23a;
}

.event-type-登出 {
  background-color: #909399;
}

.event-type-状态,
.event-type-状态变更 {
  background-color: #409eff;
}

.event-type-呼叫,
.event-type-来电 {
  background-color: #e6a23c;
}

.event-type-初始化 {
  background-color: #909399;
}

.event-type-错误 {
  background-color: #f56c6c;
}

.event-type-登录响应,
.event-type-登出响应,
.event-type-功能掩码,
.event-type-轮询,
.event-type-WebSocket {
  background-color: #9966ff;
}

.event-content {
  flex: 1;
  font-size: 14px;
  line-height: 20px;
  word-break: break-all;
}

.debug-data {
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  font-family: monospace;
  font-size: 12px;
  max-height: 200px;
  overflow: auto;
  white-space: pre-wrap;
}
</style>