import { EventEmitter } from '../core/EventEmitter';
import { RequestService } from './request';
import { ccbarDebugger } from './index';
import { loadConfig } from './config';
import { ElMessage } from 'element-plus';

/**
 * 长轮询服务类
 * 按照ccbar.js原有实现方式封装的长轮询服务
 */
export class PollingService {
  private static instance: PollingService;
  private requestService: RequestService;
  private eventEmitter: EventEmitter;
  private isLongPolling: boolean = false;
  private clearLongPollingFlag: boolean = false;
  private lastPollingTime: Date | null = null;
  private sessionId: string | null = null;
  private agentId: string | null = null;
  private pollingTimeout: number;
  private url: string;
  private baseURL: string;
  private isLogined: boolean = false;
  private failureCount: number = 0; // 连续失败次数
  private maxRetryCount: number = 5; // 最大重试次数，减少到5次
  private baseRetryDelay: number = 2000; // 初始重试延迟(ms)，增加到2秒
  private maxRetryDelay: number = 60000; // 最大重试延迟(ms)，1分钟
  private networkRecoveryTimeout: number | null = null; // 网络恢复检测定时器
  private isNetworkOffline: boolean = false; // 网络是否离线
  private retryTimer: number | null = null; // 重试定时器
  private isRecoveryMode: boolean = false; // 是否处于恢复模式

  constructor(config: {
    baseURL: string;
    pollingTimeout?: number;
    pollingUrl?: string;
    pollingInterval?: number;
    maxRetryCount?: number;
    baseRetryDelay?: number;
    maxRetryDelay?: number;
  }) {
    // 获取全局配置中的baseURL，如果传入的baseURL为空则使用全局配置
    const globalConfig = loadConfig();
    this.baseURL = globalConfig.baseURL || '/api';
    this.url = config.pollingUrl || `/AgentEvent?action=LongPolling`;
    this.pollingTimeout = config.pollingTimeout || 13000; // 默认13秒超时，与原始ccbar.js一致
    this.eventEmitter = EventEmitter.getInstance();
    this.requestService = new RequestService(
      this.pollingTimeout,
      false // 默认不开启调试
    );
    // 可配置的重试参数
    if (config.maxRetryCount !== undefined) this.maxRetryCount = config.maxRetryCount;
    if (config.baseRetryDelay !== undefined) this.baseRetryDelay = config.baseRetryDelay;
    if (config.maxRetryDelay !== undefined) this.maxRetryDelay = config.maxRetryDelay;
    
    // 监听网络状态变化
    this.setupNetworkListeners();
  }

  /**
   * 设置网络状态监听器
   */
  private setupNetworkListeners(): void {
    // 监听网络在线状态
    window.addEventListener('online', this.handleNetworkOnline.bind(this));
    
    // 监听网络离线状态
    window.addEventListener('offline', this.handleNetworkOffline.bind(this));
    
    // 初始检查网络状态
    this.isNetworkOffline = !navigator.onLine;
    if (this.isNetworkOffline) {
      ccbarDebugger('初始化时检测到网络离线', null, 'error');
    }
  }

  /**
   * 处理网络恢复在线
   */
  private handleNetworkOnline(): void {
    ccbarDebugger('网络连接已恢复', null, 'log');
    this.isNetworkOffline = false;
    
    // 清除所有计时器
    this.clearAllTimers();
    
    // 重置失败计数
    this.resetFailureCount();
    
    // 如果已登录，重新开始轮询
    if (this.isLogined && !this.clearLongPollingFlag) {
      ElMessage({
        message: '网络已恢复连接',
        type: 'success',
        duration: 3000
      });
      
      this.eventEmitter.emit('polling:networkRecovered', {
        timestamp: new Date().getTime()
      });
      
      // 延迟1秒再开始轮询，确保网络稳定
      setTimeout(() => {
        this.doLongPolling();
      }, 1000);
    }
  }

  /**
   * 处理网络离线
   */
  private handleNetworkOffline(): void {
    ccbarDebugger('网络连接已断开', null, 'error');
    this.isNetworkOffline = true;
    
    // 清除所有计时器
    this.clearAllTimers();
    
    // 停止当前轮询
    this.isLongPolling = false;
    
    // 通知用户网络已断开
    ElMessage({
      message: '网络连接已断开，请检查网络设置',
      type: 'error',
      duration: 5000
    });
    
    this.eventEmitter.emit('polling:networkOffline', {
      timestamp: new Date().getTime()
    });
    
    // 切换到网络恢复检测模式
    this.isRecoveryMode = true;
  }

  /**
   * 清除所有计时器
   */
  private clearAllTimers(): void {
    if (this.retryTimer) {
      clearTimeout(this.retryTimer);
      this.retryTimer = null;
    }
    
    if (this.networkRecoveryTimeout) {
      clearTimeout(this.networkRecoveryTimeout);
      this.networkRecoveryTimeout = null;
    }
  }

  public static getInstance(config: {
    baseURL: string;
    pollingTimeout?: number;
    pollingUrl?: string;
    pollingInterval?: number;
    maxRetryCount?: number;
    baseRetryDelay?: number;
    maxRetryDelay?: number;
  }): PollingService {
    if (!PollingService.instance) {
      PollingService.instance = new PollingService(config);
    }
    return PollingService.instance;
  }

  /**
   * 设置会话ID
   * @param sessionId 会话ID
   */
  public setSessionId(sessionId: string | null): void {
    this.sessionId = sessionId;
    ccbarDebugger(`设置会话ID: ${sessionId || 'null'}`);
  }

  /**
   * 设置座席ID
   * @param agentId 座席ID
   */
  public setAgentId(agentId: string | null): void {
    this.agentId = agentId;
    ccbarDebugger(`设置座席ID: ${agentId || 'null'}`);
  }

  /**
   * 设置登录状态
   * @param isLogined 是否已登录
   */
  public setLoginState(isLogined: boolean): void {
    this.isLogined = isLogined;
    // 重置失败计数
    if (isLogined) {
      this.resetFailureCount();
    }
  }

  /**
   * 重置失败计数
   */
  private resetFailureCount(): void {
    this.failureCount = 0;
    this.isRecoveryMode = false;
    
    // 清除所有计时器
    this.clearAllTimers();
  }

  /**
   * 计算指数退避延迟时间
   * @returns 下次重试的延迟时间(ms)
   */
  private calculateBackoffDelay(): number {
    // 指数退避算法: 初始延迟 * (2^失败次数)，但不超过最大延迟
    const delay = Math.min(
      this.baseRetryDelay * Math.pow(2, this.failureCount - 1),
      this.maxRetryDelay
    );
    // 添加一些随机抖动避免请求集中
    return delay + Math.floor(Math.random() * 1000);
  }

  /**
   * 开始长轮询
   */
  public startPolling(): void {
    ccbarDebugger('开始HTTP长轮询');
    this.eventEmitter.emit('polling:start');
    this.clearLongPollingFlag = false;
    this.resetFailureCount();

    // 如果当前网络离线，则不启动轮询
    if (this.isNetworkOffline) {
      ccbarDebugger('网络当前离线，暂不启动轮询', null, 'error');
      ElMessage({
        message: '网络当前离线，请检查网络后重试',
        type: 'warning',
        duration: 5000
      });
      this.scheduleNetworkRecoveryCheck();
      return;
    }

    // 立即进行第一次轮询
    this.doLongPolling();
  }

  /**
   * 停止长轮询
   */
  public stopPolling(): void {
    ccbarDebugger('停止HTTP长轮询');
    this.clearLongPollingFlag = true;
    this.isLongPolling = false;
    this.resetFailureCount();
    this.clearAllTimers();
    this.eventEmitter.emit('polling:stop');
  }

  /**
   * 检查网络恢复状态并恢复轮询
   * 使用更保守的检测频率，避免频繁检测消耗资源
   */
  private scheduleNetworkRecoveryCheck(): void {
    // 如果已经处于网络恢复检测模式或浏览器已检测到在线，则不重复调度
    if (this.networkRecoveryTimeout || navigator.onLine) {
      return;
    }

    ccbarDebugger('启动网络恢复检测模式', null, 'log');
    
    // 设置网络恢复检测，使用递增间隔
    const checkInterval = this.isRecoveryMode ? this.maxRetryDelay : 
                          Math.min(this.baseRetryDelay * Math.pow(2, Math.min(this.failureCount, 8)), this.maxRetryDelay);
    
    this.networkRecoveryTimeout = setTimeout(() => {
      // 先检查浏览器API提供的网络状态
      if (navigator.onLine) {
        ccbarDebugger('浏览器报告网络已恢复', null, 'log');
        this.handleNetworkOnline();
        return;
      }
      
      ccbarDebugger(`执行网络恢复检测 (检测间隔: ${checkInterval}ms)...`);
      
      // 尝试发送一个简单的请求来检测网络
      fetch(window.location.origin + '/favicon.ico', { 
        method: 'HEAD',
        cache: 'no-store',
        // 设置一个短超时，避免长时间等待
        signal: AbortSignal.timeout(3000)
      })
      .then(() => {
        ccbarDebugger('网络检测成功，网络已恢复', null, 'log');
        this.isNetworkOffline = false;
        this.resetFailureCount();
        
        if (this.isLogined && !this.clearLongPollingFlag) {
          ElMessage({
            message: '网络已恢复连接',
            type: 'success',
            duration: 3000
          });
          
          this.doLongPolling();
        }
      })
      .catch((err) => {
        // 网络仍然不可用
        ccbarDebugger(`网络仍未恢复: ${err.message}`, null, 'error');
        
        // 确保状态标记为离线
        this.isNetworkOffline = true;
        
        // 递增失败计数，但最多到10
        if (this.failureCount < 10) {
          this.failureCount++;
        }
        
        // 再次调度检查，但使用较长间隔
        this.networkRecoveryTimeout = null;
        this.scheduleNetworkRecoveryCheck();
      });
    }, checkInterval);
    
    ccbarDebugger(`已安排网络恢复检测，将在 ${checkInterval}ms 后执行`, null, 'log');
  }

  /**
   * 执行长轮询
   * 基于ccbar.js中的longPolling方法实现
   */
  private doLongPolling(): boolean {
    // 如果已经在轮询、未登录或网络已知离线，则不启动
    if (this.isLongPolling || !this.isLogined || this.isNetworkOffline) {
      if (this.isNetworkOffline) {
        ccbarDebugger('网络离线，不执行轮询', null, 'error');
        this.scheduleNetworkRecoveryCheck();
      }
      return false;
    }

    // 如果处于恢复模式，也不启动正常轮询
    if (this.isRecoveryMode) {
      ccbarDebugger('系统处于网络恢复模式，不执行常规轮询', null, 'error');
      this.scheduleNetworkRecoveryCheck();
      return false;
    }

    this.isLongPolling = true;
    this.clearLongPollingFlag = false;
    const startTime = new Date().getTime();

    const data = {
      reload: false,
      cuid: localStorage.getItem('cuid') || ''
    };

    // 构造请求参数
    const pollingParams = {
      data: data
    };

    ccbarDebugger('发送长轮询请求', data);
    this.requestService.post(this.url, pollingParams, {
      timeout: this.pollingTimeout,
      headers: {
        'Content-Type': 'application/json; charset=utf-8',
        'access-control-allow-headers': 'cuid'
      }
    }).then((result) => {
      // 处理成功响应
      this.isLongPolling = false;
      this.lastPollingTime = new Date();
      // 重置失败计数，因为请求成功了
      this.resetFailureCount();

      ccbarDebugger('长轮询成功', result);

      // 触发轮询成功事件
      this.eventEmitter.emit('polling:success', {
        time: new Date().getTime(),
        delay: new Date().getTime() - startTime,
        online: true,
        data: result.data
      });

      // 处理返回的事件
      if (result.data && result.data.events) {
        this.handlePollingEvents(result.data.events);
      }

      // 发出一个状态同步通知，确保状态始终保持最新
      this.eventEmitter.emit('polling:stateSync', {
        timestamp: new Date().getTime(),
        sessionId: this.sessionId,
        agentId: this.agentId
      });

      // 重新启动轮询 - 立即递归请求下一次，不再等待8秒
      if (this.isLogined && !this.clearLongPollingFlag) {
        // 立即发起下一次轮询
        this.doLongPolling();
      }
    }).catch((error) => {
      // 处理错误
      this.isLongPolling = false;
      // 增加失败计数
      this.failureCount++;

      ccbarDebugger(`长轮询失败 (第${this.failureCount}次)`, error, 'error');

      // 检查是否为403错误，如果是则立即停止轮询
      if (error && (error.data?.code === '403' || error.data?.resultCode === '403')) {
        ccbarDebugger('检测到403错误，立即停止轮询', error, 'error');
        this.clearLongPollingFlag = true;
        this.isLogined = false;
        this.resetFailureCount();

        // 触发403错误事件
        this.eventEmitter.emit('session:403error', {
          message: '会话已过期，需要重新登录',
          timestamp: new Date().getTime(),
          stopPolling: true,
          error: error // 添加错误详情
        });

        // 通知轮询已被强制停止
        this.eventEmitter.emit('polling:forceStop', {
          reason: '403 Forbidden - 会话已过期或无效',
          timestamp: new Date().getTime()
        });

        return; // 不再继续轮询
      }

      // 检查错误是否指示网络问题
      const isNetworkError = this.isNetworkErrorType(error);
      if (isNetworkError) {
        // 设置网络离线状态
        this.isNetworkOffline = true;
        ccbarDebugger('检测到网络错误，标记为离线状态', error, 'error');
      }

      // 触发轮询失败事件
      this.eventEmitter.emit('polling:error', {
        time: new Date().getTime(),
        delay: new Date().getTime() - startTime,
        online: !this.isNetworkOffline,
        error: error,
        retryCount: this.failureCount,
        maxRetries: this.maxRetryCount,
        isNetworkError: isNetworkError
      });

      // 检查会话是否超时
      const now = new Date();
      if (this.lastPollingTime && (now.getTime() - this.lastPollingTime.getTime() > 60000)) {
        // 超过1分钟未收到响应，触发超时
        this.eventEmitter.emit('session:timeout');
        ccbarDebugger('长轮询超时，会话可能已终止', null, 'error');
        return;
      }

      // 检查是否超过最大重试次数或网络已知离线
      if (this.failureCount >= this.maxRetryCount || this.isNetworkOffline) {
        // 进入恢复模式
        this.isRecoveryMode = true;
        
        ccbarDebugger(
          this.isNetworkOffline 
            ? '检测到网络离线，切换到网络恢复检测模式' 
            : `已达到最大重试次数(${this.maxRetryCount})，切换到网络恢复检测模式`, 
          null, 
          'error'
        );
        
        // 只在第一次进入恢复模式时通知用户
        if (this.failureCount === this.maxRetryCount || isNetworkError) {
          ElMessage({
            message: '网络连接异常，系统将在网络恢复后自动重连',
            type: 'warning',
            duration: 5000
          });
        }
        
        // 触发网络问题事件
        this.eventEmitter.emit('polling:networkIssue', {
          timestamp: new Date().getTime(),
          failureCount: this.failureCount,
          message: '网络连接异常，已暂停请求',
          isOffline: this.isNetworkOffline
        });
        
        // 切换到间歇性网络检测模式，而不是持续重试
        this.scheduleNetworkRecoveryCheck();
        return;
      }

      // 重试轮询，使用指数退避策略
      if (this.isLogined && !this.clearLongPollingFlag && !this.isRecoveryMode) {
        const retryDelay = this.calculateBackoffDelay();
        ccbarDebugger(`将在 ${retryDelay}ms 后重试轮询 (第${this.failureCount}次)`, null, 'error');
        
        // 确保清除之前的定时器
        if (this.retryTimer) {
          clearTimeout(this.retryTimer);
        }
        
        this.retryTimer = setTimeout(() => {
          this.retryTimer = null;
          this.doLongPolling();
        }, retryDelay);
      }
    });

    return true;
  }

  /**
   * 判断错误是否为网络错误
   * @param error 错误对象
   * @returns 是否为网络错误
   */
  private isNetworkErrorType(error: any): boolean {
    // 检查常见的网络错误模式
    return !navigator.onLine || 
           (error && (
             error.message === 'Network Error' || 
             error.name === 'AbortError' ||
             error.name === 'TypeError' && error.message.includes('network') ||
             error.code === 'ECONNABORTED' ||
             (error.response === undefined && error.request) ||
             (error.statusText === 'timeout' || error.statusText === 'Network Error')
           ));
  }

  /**
   * 处理轮询返回的事件
   * @param events 事件数组
   */
  private handlePollingEvents(events: any[]): void {
    if (!Array.isArray(events) || events.length === 0) return;

    events.forEach(event => {
      ccbarDebugger('处理轮询事件', event);

      // 根据接口实际返回的messageId判断事件类型
      if (event.messageId) {
        switch (event.messageId) {
          case 'notify': // 通知事件
            // 所有notify类型的消息都弹出提示框
            console.log('准备发送通知事件，完整事件对象:', JSON.stringify(event));

            // 安全检查事件数据完整性
            const notifyData = {
              agentId: event.agentId || 'unknown',
              resultCode: event.cmddata?.resultCode || 'info',
              resultDesc: event.cmddata?.resultDesc || '系统通知',
              srcMessageId: event.cmddata?.srcMessageId || 'unknown',
              timestamp: event.timestamp || new Date().getTime(),
              rawEvent: event // 添加原始事件对象以便调试
            };


            // 针对登录响应的特殊处理
            if (event.cmddata && event.cmddata.srcMessageId === 'respLogin') {
              // 登录响应，增加详细日志和错误处理
              console.log('准备发送登录响应事件，完整事件对象:', JSON.stringify(event));

              // 安全检查事件数据完整性
              const responseData = {
                agentId: event.agentId || 'unknown',
                workno: event.cmddata?.result?.workno || 'unknown',
                loginTime: event.cmddata?.result?.loginTime || new Date().toISOString(),
                resultCode: event.cmddata?.resultCode || 'error',
                resultDesc: event.cmddata?.resultDesc || '未知响应',
                timestamp: event.timestamp || new Date().getTime(),
                rawEvent: event // 添加原始事件对象以便调试
              };
              // 发送事件 - 直接将responseData对象作为第一个参数传递
              this.eventEmitter.emit('agent:loginResponse', responseData);
            } else {
              // 其他通知事件
              this.eventEmitter.emit(`notify:${event.cmddata?.srcMessageId || 'unknown'}`, event);
            }
            if (event.cmddata?.resultCode === '0') {
              if (event.cmddata?.resultDesc) {
                ElMessage({
                  message: event.cmddata?.resultDesc,
                  type: 'success',
                  plain: true,
                })
              }
            } else {
              ElMessage({
                message: event.cmddata?.resultDesc,
                type: 'error',
                plain: true,
              })
            }
            break;
          case 'agentStateSync': // 座席状态同步
            // 直接使用服务器返回的状态描述和功能掩码
            this.eventEmitter.emit('agent:stateChanged', {
              agentId: event.agentId,
              state: event.cmddata.state,
              stateDesc: event.cmddata.stateDesc,
              funcMask: event.cmddata.funcMask,
              timestamp: event.timestamp
            });
            break;
          case 'callEventSync':
            this.eventEmitter.emit('call:eventSync', {
              agentId: event.agentId, // 工号
              callId: event.cmddata.event.callId, // 呼叫ID
              callEventId: event.cmddata.callEventId, // 呼叫事件ID
              entId: event.cmddata.event.entId, // 企业ID
              caller: event.cmddata.event.caller, // 主叫
              called: event.cmddata.event.called, // 被叫
              custPhone: event.cmddata.event.custPhone, // 客户电话
            });
            break;

          default:
            // 其他事件直接转发
            this.eventEmitter.emit(`polling:${event.messageId}`, event);
            break;
        }
      }
    });

    // 触发批量事件处理完成事件
    this.eventEmitter.emit('polling:events', events);
  }

  /**
   * 添加事件监听
   * @param event 事件名称
   * @param callback 回调函数
   */
  public on(event: string, callback: Function): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听
   * @param event 事件名称
   * @param callback 回调函数
   */
  public off(event: string, callback: Function): void {
    this.eventEmitter.off(event, callback);
  }

  /**
   * 一次性事件监听
   * @param event 事件名称
   * @param callback 回调函数
   */
  public once(event: string, callback: Function): void {
    this.eventEmitter.once(event, callback);
  }
} 
