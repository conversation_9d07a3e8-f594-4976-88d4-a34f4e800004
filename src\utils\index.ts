import md5 from 'md5';

/**
 * MD5加密
 * @param str 要加密的字符串
 */
export function hashMd5(str: string): string {
  return md5(str);
}

/**
 * 显示提示消息
 * @param msg 消息内容
 * @param time 显示时间（毫秒）
 */
export function tipsMsg(msg: string, time: number = 2000): void {
  console.log('[CCBar提示]', msg);
  // 在页面上显示提示信息
  const existingTip = document.getElementById('ccbar-tip');
  if (existingTip) {
    document.body.removeChild(existingTip);
  }
  
  const tipElement = document.createElement('div');
  tipElement.id = 'ccbar-tip';
  tipElement.style.position = 'fixed';
  tipElement.style.top = '20px';
  tipElement.style.left = '50%';
  tipElement.style.transform = 'translateX(-50%)';
  tipElement.style.padding = '10px 20px';
  tipElement.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
  tipElement.style.color = '#fff';
  tipElement.style.borderRadius = '4px';
  tipElement.style.zIndex = '9999';
  tipElement.style.transition = 'opacity 0.3s ease-in-out';
  tipElement.textContent = msg;
  
  document.body.appendChild(tipElement);
  
  setTimeout(() => {
    tipElement.style.opacity = '0';
    setTimeout(() => {
      if (document.body.contains(tipElement)) {
        document.body.removeChild(tipElement);
      }
    }, 300);
  }, time);
}

/**
 * 调试日志
 * @param msg 日志消息
 * @param data 附加数据或错误对象
 * @param type 日志类型
 */
export function ccbarDebugger(msg: string, data?: any, type: 'log' | 'error' = 'log'): void {
  const debug = localStorage.getItem('ccbarDebug') === 'true';
  if (debug) {
    if (type === 'error') {
      console.error(`[CCBar] ${msg}`, data);
    } else {
      console.log(`[CCBar] ${msg}`, data);
    }
  }
}

/**
 * 防抖函数
 * @param fn 要执行的函数
 * @param delay 延迟时间（毫秒）
 */
export function debounce<T extends (...args: any[]) => any>(fn: T, delay: number): (...args: Parameters<T>) => void {
  let timer: ReturnType<typeof setTimeout> | null = null;
  
  return function(this: any, ...args: Parameters<T>): void {
    const context = this;
    
    if (timer) {
      clearTimeout(timer);
    }
    
    timer = setTimeout(() => {
      fn.apply(context, args);
    }, delay);
  };
}

/**
 * 节流函数
 * @param fn 要执行的函数
 * @param threshold 阈值时间（毫秒）
 */
export function throttle<T extends (...args: any[]) => any>(fn: T, threshold: number = 250): (...args: Parameters<T>) => void {
  let last: number;
  let deferTimer: ReturnType<typeof setTimeout> | null = null;
  
  return function(this: any, ...args: Parameters<T>): void {
    const context = this;
    const now = Date.now();
    
    if (last && now < last + threshold) {
      if (deferTimer) {
        clearTimeout(deferTimer);
      }
      
      deferTimer = setTimeout(() => {
        last = now;
        fn.apply(context, args);
      }, threshold);
    } else {
      last = now;
      fn.apply(context, args);
    }
  };
}

/**
 * 日期格式化
 * @param date 日期对象
 * @param format 格式字符串
 */
export function formatDate(date: Date, format: string): string {
  const o: Record<string, number> = {
    'M+': date.getMonth() + 1,                 // 月份
    'd+': date.getDate(),                      // 日
    'h+': date.getHours(),                     // 小时
    'm+': date.getMinutes(),                   // 分
    's+': date.getSeconds(),                   // 秒
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    'S': date.getMilliseconds()                // 毫秒
  };
  
  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
  }
  
  for (const k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(
        RegExp.$1, 
        RegExp.$1.length === 1 ? 
          String(o[k]) : 
          ('00' + o[k]).substr(String(o[k]).length)
      );
    }
  }
  
  return format;
}

/**
 * 对象深拷贝
 * @param obj 要拷贝的对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  const result: any = Array.isArray(obj) ? [] : {};
  
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      result[key] = deepClone(obj[key]);
    }
  }
  
  return result as T;
}

/**
 * 替换电话号码，保护隐私
 * @param phone 原始电话号码
 * @param displayPhone 要显示的电话号码
 */
export function replacePhoneNum(phone: string, displayPhone: string): string {
  if (phone && phone.startsWith('#')) {
    return advReplace(displayPhone, 3, 4);
  } else {
    return displayPhone;
  }
}

/**
 * 高级替换，将指定位置的字符替换为指定字符
 * @param text 原始文本
 * @param start 开始位置
 * @param length 长度
 * @param placeText 替换字符
 */
export function advReplace(text: string, start: number, length: number, placeText: string = '*'): string {
  if (!text) return '';
  
  text = String(text);
  
  // 开始位置超出文本长度，直接返回原文本
  if (Math.abs(start) > text.length) return text;
  
  if (start > 0) {
    const prefix = text.substr(0, start);
    const end = start + length;
    const isLongText = end > text.length;
    const suffix = isLongText ? '' : text.substr(end);
    const replaceLength = isLongText ? text.length - start : length;
    
    return prefix + placeText.repeat(replaceLength) + suffix;
  } else {
    const end = text.substr(start);
    const startLen = text.length + start - length;
    const replaceLen = startLen > 0 ? length : length + startLen;
    const startIndex = startLen > 0 ? startLen : 0;
    const startText = text.substr(0, startIndex);
    
    return startText + placeText.repeat(replaceLen) + end;
  }
} 