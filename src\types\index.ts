export interface CCBarConfig {
  baseURL?: string;
  wsURL?: string;
  timeout?: number;
  autoReconnect?: boolean;
  maxReconnectAttempts?: number;
  reconnectInterval?: number;
  heartbeatInterval?: number;
  autoReady?: boolean;
  pollingInterval?: number;
  entId?: string;
  loginKey?: string;
  productId?: string;
  debug?: boolean;
}

export interface LoginParams {
  username: string;
  password?: string;
  phone?: string;
  readyMode?: string;
  workMode?: string;
  autoAnswer?: boolean;
  force?: boolean;
  version?: string;
  skillId?: string;
  credential?: string;
  phoneType?: string;
}

export interface AgentState {
  state: string;
  workMode: string;
  stateDesc: string;
  notifyContent: string;
  resultDesc: string | null;
  funcMask: Record<string, boolean>;
}

export interface CallEvent {
  callEventId: string;
  event: {
    caller: string;
    called: string;
    custPhone: string;
    displayCustPhone: string;
    createCause?: string;
    userData?: any;
    areaInfo?: {
      name: string;
      [key: string]: any;
    };
  };
  clearCause?: string;
}

export interface NotifyEvent {
  srcMessageId: string;
  result: string;
  resultCode: string;
  resultDesc: string;
  notifyContent?: string;
}

export interface CCBarResponse<T = any> {
  state: boolean;
  msg: string;
  data: {
    code: string;
    content: string;
    result?: T;
  };
}

export interface AgentInfo {
  agentId: string;
  agentName: string;
  funcMask: Record<string, boolean>;
  state: string;
  stateDesc: string;
  workMode: string;
  phoneType: string;
  sbcAddr?: string;
}

export interface CallerInfo {
  caller: string;
  called: string;
  custPhone: string;
  displayCustPhone: string;
  realCustPhone?: string;
}

export enum WorkModeType {
  INBOUND = 'inbound',
  OUTBOUND = 'outbound',
  PDS = 'pdsbound',
  ALL = 'all'
}

export enum AppStateType {
  READY = 'ready',
  NOT_READY = 'notReady',
  UNKNOWN = 'unknown'
}

export enum AgentStateType {
  IDLE = 'IDLE',
  ALERTING = 'ALERTING',
  TALK = 'TALK',
  BUSY = 'BUSY',
  LOGOFF = 'LOGOFF',
  OCCUPY = 'OCCUPY',
  WORKNOTREADY = 'WORKNOTREADY',
  HELD = 'HELD',
  CONFERENCED = 'CONFERENCED',
  CONSULTED = 'CONSULTED',
  MONITORED = 'MONITORED',
  MUTE = 'MUTE',
  UNMUTE = 'UNMUTE'
}

export enum FuncMaskType {
  LOGON = 'logon',
  LOGOFF = 'logoff',
  MAKECALL = 'makecall',
  ANSWERCALL = 'answercall',
  CLEARCALL = 'clearcall',
  HOLDCALL = 'holdcall',
  UNHOLDCALL = 'unholdcall',
  TRANSFERCALL = 'transfercall',
  CONSULTATIONCALL = 'consultationcall',
  CONFERENCECALL = 'conferencecall',
  AGENTREADY = 'agentready',
  AGENTNOTREADY = 'agentnotready',
  WORKREADY = 'workready',
  CANCELCONSULTATION = 'cancelconsultation',
  COMPLETETRANSFER = 'completetransfer',
  MUTECALL = 'mutecall',
  UNMUTECALL = 'unmutecall'
}

export enum CallEventType {
  ALERTING = 'evtAlertRouted',
  CONNECTED = 'evtEstablished',
  DISCONNECTED = 'evtReleased',
  HELD = 'evtHeld',
  RETRIEVED = 'evtRetrieved',
  TRANSFERRED = 'evtTransferred',
  CONSULTED = 'evtConsulted',
  TRANSFERRED_COMPLETE = 'evtTransferComplete',
  CONSULT_END = 'evtConsultEnd'
}

export enum NotifyEventType {
  LOGIN = 'respLogin',
  LOGOUT = 'respLogout',
  MAKECALL = 'respMakeCall',
  ANSWERCALL = 'respAnswerCall',
  CLEARCALL = 'respClearCall',
  READY = 'respReady',
  NOTREADY = 'respNotReady',
  WORKREADY = 'respWorkReady',
  WORKNOTREADY = 'respWorkNotReady',
  TRANSFERCALL = 'respTransferCall',
  CONSULTCALL = 'respConsultCall',
  CONFERENCECALL = 'respConferenceCall'
}

export interface IncomingCallParams {
  callId: string;
  callType: string;
  caller: string;
  callerName?: string;
  skills?: string[];
  userData?: Record<string, any>;
}

export interface CallConnectedParams {
  callId: string;
  timestamp: number;
}

export interface CallEndedParams {
  callId: string;
  timestamp: number;
  duration?: number;
  reason?: string;
}

export interface AgentStateChangeParams {
  state: AgentStateType;
  reason?: string;
}

export interface TransferCallParams {
  targetType: 'agent' | 'phone';
  agentId?: string;
  agentPhone?: string;
  phoneNumber?: string;
  displayNumber?: string;
  isDirectTransfer?: boolean;
}

/**
 * CCBar全局对象类型，包含service实例
 */
export interface CCBarWithService {
  service: import('../core/CCBarService').CCBarService;
  config?: CCBarConfig;
}

export interface ConsultCallParams {
  targetType: 'agent' | 'number';
  agentId?: string;
  agentPhone?: string;
  phoneNumber?: string;
  displayNumber?: string;
} 