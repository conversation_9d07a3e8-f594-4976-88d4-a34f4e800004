import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import App from './App.vue'
import './style.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { CCBar, CCBarService } from './index'
import router from './router'

// 配置与初始化
const config = {
  debug: true,
  baseURL: '/api/yc-ccbar-v1',
  wsURL: '/ws'
};

// 初始化CCBarService实例
const ccbarService = CCBarService.getInstance(config);

// 将service附加到CCBar对象上
// @ts-ignore
CCBar.service = ccbarService;

// 确保window.CCBar对象被正确设置
if (typeof window !== 'undefined') {
  console.log('[CCBar] 全局CCBar对象初始化, CCBar.getInstance可用');
  // @ts-ignore
  window.CCBar = CCBar;
}

const app = createApp(App)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(ElementPlus)

// 使用 CCBar 插件
app.use(CCBar as any, config)

app.use(router)

app.mount('#app') 