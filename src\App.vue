<template>
  <div class="app-container">
    <!-- 顶部导航栏 -->
    <header class="app-header">
      <div class="header-content">
        <!-- <h1 class="app-title">CCBar接口演示</h1> -->
        <el-menu
          :default-active="activeRoute"
          mode="horizontal"
          router
          class="app-menu"
        >
          <el-menu-item index="/">签入演示</el-menu-item>
          <el-menu-item index="/config">
            <el-icon><setting /></el-icon>
            配置管理
          </el-menu-item>
          <el-menu-item index="/ccbar-demo">
            <el-icon><phone /></el-icon>
            话务条演示
          </el-menu-item>
          <el-menu-item index="/supervisor">
            <el-icon><phone /></el-icon>
            班长功能演示
          </el-menu-item>
        </el-menu>
      </div>
    </header>
    
    <!-- 主内容区 -->
    <main class="app-main">
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </main>
    
    <!-- 页脚 -->
    <footer class="app-footer">
      <p>CCBar接口演示 - 基于真实接口对接</p>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import { Setting, Phone } from '@element-plus/icons-vue';

// 获取当前激活的路由
const route = useRoute();
const activeRoute = computed(() => route.path);
</script>

<style>
html, body {
  margin: 0;
  padding: 0;
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background-color: #f5f7fa;
  color: #2c3e50;
  height: 100%;
}

#app {
  height: 100%;
}

.app-container {
  min-height: 100%;
  display: flex;
  flex-direction: column;
}

.app-header {
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.app-title {
  margin: 15px 0;
  text-align: center;
  color: #409eff;
}

.app-menu {
  display: flex;
  justify-content: center;
}

.app-main {
  flex: 1;
  padding: 20px 0;
}

.app-footer {
  background-color: #303133;
  color: #909399;
  text-align: center;
  padding: 20px 0;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 全局样式 */
h1 {
  text-align: center;
  color: #409eff;
  margin-bottom: 10px;
  font-size: 1.8rem;
}

h2 {
  font-size: 1.5rem;
  margin: 0 0 20px 0;
  color: #303133;
  text-align: center;
}

h3 {
  font-size: 1.2rem;
  color: #606266;
}

h4 {
  font-size: 1.1rem;
  color: #606266;
}
</style>
