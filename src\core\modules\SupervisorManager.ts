/**
 * 班长操作管理模块
 * 负责班长/主管对座席的监听、强插、强拆、密语等操作
 */

import { ISupervisorManager, IEventManager } from '../interfaces';
import { CCBarResponse } from '../../types';
import { RequestService } from '../../utils/request';
import { ccbarDebugger } from '../../utils';

/**
 * 班长操作管理器实现类
 */
export class SupervisorManager implements ISupervisorManager {
  private requestService: RequestService;
  private eventManager: IEventManager;
  private agentId: string;
  private currentCallId: string | null = null;

  /**
   * 构造函数
   * @param requestService HTTP请求服务
   * @param eventManager 事件管理器
   * @param agentId 班长座席ID
   */
  constructor(
    requestService: RequestService,
    eventManager: IEventManager,
    agentId: string
  ) {
    this.requestService = requestService;
    this.eventManager = eventManager;
    this.agentId = agentId;

    // 监听通话事件，更新当前呼叫ID
    this.eventManager.on('call:eventSync', (data: any) => {
      if (data && data.callId) {
        this.currentCallId = data.callId;
      }
    });
  }

  /**
   * 更新会话ID
   * @param sessionId 新的会话ID
   */
  public updateSessionId(sessionId: string): void {
    // sessionId在班长功能中不再使用
    ccbarDebugger('SupervisorManager.updateSessionId被调用，但sessionId在班长功能中未使用');
  }

  /**
   * 开始监听
   * @param agentId 座席ID
   * @param callId 通话ID，不传则使用当前通话
   * @param options 附加选项
   * @returns 监听结果
   */
  public async startMonitor(agentId: string, callId?: string, options?: any): Promise<CCBarResponse> {
    try {
      // 构建监听请求参数
      const monitorParams = {
        data: {
          messageId: 'cmdStartMonitorCall',
          agentId: agentId, // 被监听的座席ID
          cuid: localStorage.getItem('cuid') || '',
          timestamp: new Date().getTime(),
          ...(options || {})
        }
      };

      // 发送监听请求
      const result = await this.requestService.post('/AgentEvent?action=event', monitorParams, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8'
        }
      });

      // 处理响应结果
      if (result.state) {
        // 触发监听开始事件
        this.eventManager.emit('supervisor:monitorStarted', {
          supervisorId: this.agentId,
          agentId: agentId,
          callId: callId || this.currentCallId,
          timestamp: new Date().getTime()
        });

        return {
          state: true,
          msg: '监听已开始',
          data: {
            code: 'succ',
            content: '监听已开始',
            result: result.state
          }
        };
      } else {
        return {
          state: false,
          msg: result.data?.content || '开始监听失败',
          data: {
            code: 'fail',
            content: result.data?.content || '开始监听失败',
            result: null
          }
        };
      }
    } catch (error: any) {
      ccbarDebugger('开始监听异常', error);
      return {
        state: false,
        msg: error.message || '开始监听失败',
        data: {
          code: 'error',
          content: error.message || '开始监听失败',
          result: null
        }
      };
    }
  }

  /**
   * 结束监听
   * @param agentId 座席ID
   * @param callId 通话ID，不传则使用当前通话
   * @param options 附加选项
   * @returns 操作结果
   */
  public async stopMonitor(agentId: string, callId?: string, options?: any): Promise<CCBarResponse> {
    try {
      // 构建结束监听请求参数
      const stopMonitorParams = {
        data: {
          messageId: 'cmdStopMonitorCall',
          agentId: agentId, // 被监听的座席ID
          cuid: localStorage.getItem('cuid') || '',
          timestamp: new Date().getTime(),
          ...(options || {})
        }
      };

      // 发送结束监听请求
      const result = await this.requestService.post('/AgentEvent?action=event', stopMonitorParams, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8'
        }
      });

      // 处理响应结果
      if (result.state) {
        // 触发监听结束事件
        this.eventManager.emit('supervisor:monitorStopped', {
          supervisorId: this.agentId,
          agentId: agentId,
          callId: callId || this.currentCallId,
          timestamp: new Date().getTime()
        });

        return {
          state: true,
          msg: '监听已结束',
          data: {
            code: 'succ',
            content: '监听已结束',
            result: result.state
          }
        };
      } else {
        return {
          state: false,
          msg: result.data?.content || '结束监听失败',
          data: {
            code: 'fail',
            content: result.data?.content || '结束监听失败',
            result: null
          }
        };
      }
    } catch (error: any) {
      ccbarDebugger('结束监听异常', error);
      return {
        state: false,
        msg: error.message || '结束监听失败',
        data: {
          code: 'error',
          content: error.message || '结束监听失败',
          result: null
        }
      };
    }
  }

  /**
 * 开始强拆
 * @param agentId 座席ID
 * @param callId 通话ID，不传则使用当前通话
 * @param options 附加选项
 * @returns 操作结果
 */
  public async ClearForceCall(agentId: string, callId?: string, options?: any): Promise<CCBarResponse> {
    try {
      // 构建结束监听请求参数
      const stopMonitorParams = {
        data: {
          messageId: 'cmdClearForceCall',
          agentId: agentId, // 被监听的座席ID
          cuid: localStorage.getItem('cuid') || '',
          timestamp: new Date().getTime(),
          ...(options || {})
        }
      };

      // 发送结束监听请求
      const result = await this.requestService.post('/AgentEvent?action=event', stopMonitorParams, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8'
        }
      });

      // 处理响应结果
      if (result.state) {
        // 触发监听结束事件
        this.eventManager.emit('supervisor:clearForceCall', {
          supervisorId: this.agentId,
          agentId: agentId,
          callId: callId || this.currentCallId,
          timestamp: new Date().getTime()
        });

        return {
          state: true,
          msg: '强拆已开始',
          data: {
            code: 'succ',
            content: '强拆已开始',
            result: result.state
          }
        };
      } else {
        return {
          state: false,
          msg: result.data?.content || '强拆失败',
          data: {
            code: 'fail',
            content: result.data?.content || '强拆失败',
            result: null
          }
        };
      }
    } catch (error: any) {
      ccbarDebugger('强拆异常', error);
      return {
        state: false,
        msg: error.message || '强拆失败',
        data: {
          code: 'error',
          content: error.message || '强拆失败',
          result: null
        }
      };
    }
  }

  /**
   * 开始强插
   * @param agentId 座席ID
   * @param callId 通话ID，不传则使用当前通话
   * @param options 附加选项
   * @returns 操作结果
   */
  public async startIntercept(agentId: string, callId?: string, options?: any): Promise<CCBarResponse> {
    try {
      // 构建强插请求参数
      const interceptParams = {
        data: {
          messageId: 'cmdInterventCall',
          agentId: agentId, // 被强插的座席ID
          cuid: localStorage.getItem('cuid') || '',
          timestamp: new Date().getTime(),
          ...(options || {})
        }
      };

      // 发送强插请求
      const result = await this.requestService.post('/AgentEvent?action=event', interceptParams, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8'
        }
      });

      // 处理响应结果
      if (result.state) {
        // 触发强插开始事件
        this.eventManager.emit('supervisor:forceCallStarted', {
          supervisorId: this.agentId,
          agentId: agentId,
          callId: callId || this.currentCallId,
          timestamp: new Date().getTime()
        });

        return {
          state: true,
          msg: '强插已开始',
          data: {
            code: 'succ',
            content: '强插已开始',
            result: result.state
          }
        };
      } else {
        return {
          state: false,
          msg: result.data?.content || '开始强插失败',
          data: {
            code: 'fail',
            content: result.data?.content || '开始强插失败',
            result: null
          }
        };
      }
    } catch (error: any) {
      ccbarDebugger('开始强插异常', error);
      return {
        state: false,
        msg: error.message || '开始强插失败',
        data: {
          code: 'error',
          content: error.message || '开始强插失败',
          result: null
        }
      };
    }
  }

  /**
   * 结束强插
   * @param agentId 座席ID
   * @param callId 通话ID，不传则使用当前通话
   * @param options 附加选项
   * @returns 操作结果
   */
  public async stopIntercept(agentId: string, callId?: string, options?: any): Promise<CCBarResponse> {
    try {
      // 构建结束强插请求参数
      const stopInterceptParams = {
        data: {
          messageId: 'cmdStopInterventCall',
          agentId: agentId, // 被强插的座席ID
          cuid: localStorage.getItem('cuid') || '',
          timestamp: new Date().getTime(),
          ...(options || {})
        }
      };

      // 发送结束强插请求
      const result = await this.requestService.post('/AgentEvent?action=event', stopInterceptParams, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8'
        }
      });

      // 处理响应结果
      if (result.state) {
        // 触发强插结束事件
        this.eventManager.emit('supervisor:forceCallStopped', {
          supervisorId: this.agentId,
          agentId: agentId,
          callId: callId || this.currentCallId,
          timestamp: new Date().getTime()
        });

        return {
          state: true,
          msg: '强插已结束',
          data: {
            code: 'succ',
            content: '强插已结束',
            result: result.state
          }
        };
      } else {
        return {
          state: false,
          msg: result.data?.content || '结束强插失败',
          data: {
            code: 'fail',
            content: result.data?.content || '结束强插失败',
            result: null
          }
        };
      }
    } catch (error: any) {
      ccbarDebugger('结束强插异常', error);
      return {
        state: false,
        msg: error.message || '结束强插失败',
        data: {
          code: 'error',
          content: error.message || '结束强插失败',
          result: null
        }
      };
    }
  }

  /**
   * 强制置忙
   * @param agentId 座席ID
   * @param reason 原因，可选
   * @param options 附加选项
   * @returns 操作结果
   */
  public async forceBusy(agentId: string, reason?: string, options?: any): Promise<CCBarResponse> {
    try {
      // 构建强制置忙请求参数
      const forceBusyParams = {
        data: {
          messageId: 'cmdChangeAgentStatus',
          agentId: agentId, // 被强制置忙的座席ID
          reason: reason || '主管强制置忙',
          actionType: 'forcenotready',
          cuid: localStorage.getItem('cuid') || '',
          timestamp: new Date().getTime(),
          ...(options || {})
        }
      };

      // 发送强制置忙请求
      const result = await this.requestService.post('/AgentEvent?action=event', forceBusyParams, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8'
        }
      });

      // 处理响应结果
      if (result.state) {
        // 触发强制置忙事件
        this.eventManager.emit('supervisor:forceBusy', {
          supervisorId: this.agentId,
          agentId: agentId,
          reason: reason,
          timestamp: new Date().getTime()
        });

        return {
          state: true,
          msg: '强制置忙已执行',
          data: {
            code: 'succ',
            content: '强制置忙已执行',
            result: result.state
          }
        };
      } else {
        return {
          state: false,
          msg: result.data?.content || '强制置忙失败',
          data: {
            code: 'fail',
            content: result.data?.content || '强制置忙失败',
            result: null
          }
        };
      }
    } catch (error: any) {
      ccbarDebugger('强制置忙异常', error);
      return {
        state: false,
        msg: error.message || '强制置忙失败',
        data: {
          code: 'error',
          content: error.message || '强制置忙失败',
          result: null
        }
      };
    }
  }

  /**
   * 强制置闲
   * @param agentId 座席ID
   * @param options 附加选项
   * @returns 操作结果
   */
  public async forceIdle(agentId: string, options?: any): Promise<CCBarResponse> {
    try {
      // 构建强制置闲请求参数
      const forceIdleParams = {
        data: {
          messageId: 'cmdChangeAgentStatus',
          agentId: agentId, // 被强制置闲的座席ID
          cuid: localStorage.getItem('cuid') || '',
          actionType: 'forceready',
          timestamp: new Date().getTime(),
          ...(options || {})
        }
      };

      // 发送强制置闲请求
      const result = await this.requestService.post('/AgentEvent?action=event', forceIdleParams, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8'
        }
      });
      console.log('强制置闲请求结果', result, 'ccbar');
      // 处理响应结果
      if (result.state) {
        // 触发强制置闲事件
        this.eventManager.emit('supervisor:forceIdle', {
          supervisorId: this.agentId,
          agentId: agentId,
          timestamp: new Date().getTime()
        });

        return {
          state: true,
          msg: '强制置闲已执行',
          data: {
            code: 'succ',
            content: '强制置闲已执行',
            result: result.state
          }
        };
      } else {
        return {
          state: false,
          msg: result.data?.content || '强制置闲失败',
          data: {
            code: 'fail',
            content: result.data?.content || '强制置闲失败',
            result: null
          }
        };
      }
    } catch (error: any) {
      ccbarDebugger('强制置闲异常', error);
      return {
        state: false,
        msg: error.message || '强制置闲失败',
        data: {
          code: 'error',
          content: error.message || '强制置闲失败',
          result: null
        }
      };
    }
  }

  /**
   * 开始密语
   * @param agentId 座席ID
   * @param callId 通话ID，不传则使用当前通话
   * @param options 附加选项
   * @returns 操作结果
   */
  public async startWhisper(agentId: string, callId?: string, options?: any): Promise<CCBarResponse> {
    try {
      // 构建密语请求参数
      const whisperParams = {
        data: {
          messageId: 'cmdSecretlyTalk',
          agentId: agentId, // 被密语的座席ID
          cuid: localStorage.getItem('cuid') || '',
          timestamp: new Date().getTime(),
          ...(options || {})
        }
      };

      // 发送密语请求
      const result = await this.requestService.post('/AgentEvent?action=event', whisperParams, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8'
        }
      });

      // 处理响应结果
      if (result.state) {
        // 触发密语开始事件
        this.eventManager.emit('supervisor:whisperStarted', {
          supervisorId: this.agentId,
          agentId: agentId,
          callId: callId || this.currentCallId,
          timestamp: new Date().getTime()
        });

        return {
          state: true,
          msg: '密语已开始',
          data: {
            code: 'succ',
            content: '密语已开始',
            result: result.state
          }
        };
      } else {
        return {
          state: false,
          msg: result.data?.content || '开始密语失败',
          data: {
            code: 'fail',
            content: result.data?.content || '开始密语失败',
            result: null
          }
        };
      }
    } catch (error: any) {
      ccbarDebugger('开始密语异常', error);
      return {
        state: false,
        msg: error.message || '开始密语失败',
        data: {
          code: 'error',
          content: error.message || '开始密语失败',
          result: null
        }
      };
    }
  }

  /**
   * 结束密语
   * @param agentId 座席ID
   * @param callId 通话ID，不传则使用当前通话
   * @param options 附加选项
   * @returns 操作结果
   */
  public async stopWhisper(agentId: string, callId?: string, options?: any): Promise<CCBarResponse> {
    try {
      // 构建结束密语请求参数
      const stopWhisperParams = {
        data: {
          messageId: 'cmdStopSecretlyTalk',
          agentId: agentId, // 被密语的座席ID
          cuid: localStorage.getItem('cuid') || '',
          timestamp: new Date().getTime(),
          ...(options || {})
        }
      };

      // 发送结束密语请求
      const result = await this.requestService.post('/AgentEvent?action=event', stopWhisperParams, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8'
        }
      });

      // 处理响应结果
      if (result.state) {
        // 触发密语结束事件
        this.eventManager.emit('supervisor:whisperStopped', {
          supervisorId: this.agentId,
          agentId: agentId,
          callId: callId || this.currentCallId,
          timestamp: new Date().getTime()
        });

        return {
          state: true,
          msg: '密语已结束',
          data: {
            code: 'succ',
            content: '密语已结束',
            result: result.state
          }
        };
      } else {
        return {
          state: false,
          msg: result.data?.content || '结束密语失败',
          data: {
            code: 'fail',
            content: result.data?.content || '结束密语失败',
            result: null
          }
        };
      }
    } catch (error: any) {
      ccbarDebugger('结束密语异常', error);
      return {
        state: false,
        msg: error.message || '结束密语失败',
        data: {
          code: 'error',
          content: error.message || '结束密语失败',
          result: null
        }
      };
    }
  }

  /**
   * 拦截通话
   * @param agentId 要拦截的座席ID
   * @param callId 通话ID，不传则使用当前通话
   * @param options 附加选项
   * @returns 操作结果
   */
  public async interceptCall(agentId: string, callId?: string, options?: any): Promise<CCBarResponse> {
    try {
      // 构建拦截请求参数
      const interceptParams = {
        data: {
          messageId: 'cmdIntercept',
          agentId: agentId, // 被强制置闲的座席ID
          cuid: localStorage.getItem('cuid') || '',
          timestamp: new Date().getTime(),
          ...(options || {})
        }
      };

      // 发送拦截请求
      const result = await this.requestService.post('/AgentEvent?action=event', interceptParams, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8'
        }
      });

      // 处理响应结果
      if (result.state) {
        // 触发拦截事件
        this.eventManager.emit('supervisor:callIntercepted', {
          supervisorId: this.agentId,
          agentId: agentId,
          callId: callId || this.currentCallId,
          timestamp: new Date().getTime()
        });

        return {
          state: true,
          msg: '通话已拦截',
          data: {
            code: 'succ',
            content: '通话已拦截',
            result: result.state
          }
        };
      } else {
        return {
          state: false,
          msg: result.data?.content || '拦截通话失败',
          data: {
            code: 'fail',
            content: result.data?.content || '拦截通话失败',
            result: null
          }
        };
      }
    } catch (error: any) {
      ccbarDebugger('拦截通话异常', error);
      return {
        state: false,
        msg: error.message || '拦截通话失败',
        data: {
          code: 'error',
          content: error.message || '拦截通话失败',
          result: null
        }
      };
    }
  }

  /**
   * 强制签出
   * @param agentId 座席ID
   * @param reason 原因，可选
   * @param options 附加选项
   * @returns 操作结果
   */
  public async forceLogout(agentId: string, reason?: string, options?: any): Promise<CCBarResponse> {
    try {
      // 构建强制签出请求参数
      const forceLogoutParams = {
        data: {
          messageId: 'cmdChangeAgentStatus',
          agentId: agentId, // 被强制签出的座席ID
          reason: reason || '主管强制签出',
          actionType: 'forcelogoff',
          cuid: localStorage.getItem('cuid') || '',
          timestamp: new Date().getTime(),
          ...(options || {})
        }
      };

      // 发送强制签出请求
      const result = await this.requestService.post('/AgentEvent?action=event', forceLogoutParams, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8'
        }
      });

      // 处理响应结果
      if (result.state) {
        // 触发强制签出事件
        this.eventManager.emit('supervisor:forceLogout', {
          supervisorId: this.agentId,
          agentId: agentId,
          reason: reason,
          timestamp: new Date().getTime()
        });

        return {
          state: true,
          msg: '强制签出已执行',
          data: {
            code: 'succ',
            content: '强制签出已执行',
            result: result.state
          }
        };
      } else {
        return {
          state: false,
          msg: result.data?.content || '强制签出失败',
          data: {
            code: 'fail',
            content: result.data?.content || '强制签出失败',
            result: null
          }
        };
      }
    } catch (error: any) {
      ccbarDebugger('强制签出异常', error);
      return {
        state: false,
        msg: error.message || '强制签出失败',
        data: {
          code: 'error',
          content: error.message || '强制签出失败',
          result: null
        }
      };
    }
  }
} 