<!--
SupervisorExample.vue - 班长操作演示组件

这个组件展示了如何使用SupervisorManager实现班长/主管操作功能：
1. 监听座席通话
2. 强插通话
3. 密语功能
4. 强制改变座席状态
5. 拦截通话
-->

<template>
  <div class="supervisor-example">
    <h1>班长操作演示</h1>
    
    <div class="tabs-container">
      <div class="tabs-header">
        <div class="tab" 
            :class="{ active: activeTab === 'ccbar' }" 
            @click="activeTab = 'ccbar'">
          座席控制面板
        </div>
        <div class="tab" 
            :class="{ active: activeTab === 'supervisor' }" 
            @click="activeTab = 'supervisor'">
          班长操作面板
        </div>
      </div>
      
      <div class="tabs-content">
        <!-- 座席控制面板 -->
        <div v-if="activeTab === 'ccbar'" class="tab-pane">
          <CCBarExample />
        </div>
        
        <!-- 班长操作面板 -->
        <div v-if="activeTab === 'supervisor'" class="tab-pane">
          <!-- 班长功能控制面板 -->
          <div class="supervisor-panel">
            <h2>班长操作面板</h2>
            
            <!-- 模拟登录状态提示 -->
            <div class="login-status" :class="{ 'logged-in': isLoggedIn, 'logged-out': !isLoggedIn }">
              <span v-if="isLoggedIn">班长已登录，可以执行操作</span>
              <span v-else>请先在座席控制面板中登录</span>
            </div>
            
            <!-- 目标座席信息输入 -->
            <div class="target-agent-input">
              <div class="input-group">
                <label>目标座席工号:</label>
                <input type="text" v-model="targetAgentId" placeholder="请输入座席工号" />
              </div>
              <div class="input-group">
                <label>通话ID:</label>
                <input type="text" v-model="targetCallId" placeholder="可选，不填则使用当前通话" />
              </div>
            </div>
            
            <!-- 功能按钮组 -->
            <div class="function-groups">
              <!-- 监听功能 -->
              <div class="function-group">
                <h3>监听功能</h3>
                <div class="button-group">
                  <button class="btn btn-primary" @click="startMonitor" :disabled="!isLoggedIn || !targetAgentId">开始监听</button>
                  <button class="btn btn-danger" @click="stopMonitor" :disabled="!isLoggedIn || !targetAgentId">结束监听</button>
                </div>
              </div>
              
              <!-- 强插功能 -->
              <div class="function-group">
                <h3>强插功能</h3>
                <div class="button-group">
                  <button class="btn btn-primary" @click="startIntercept" :disabled="!isLoggedIn || !targetAgentId">开始强插</button>
                  <button class="btn btn-danger" @click="stopIntercept" :disabled="!isLoggedIn || !targetAgentId">结束强插</button>
                </div>
              </div>
              
              <!-- 密语功能 -->
              <div class="function-group">
                <h3>密语功能</h3>
                <div class="button-group">
                  <button class="btn btn-primary" @click="startWhisper" :disabled="!isLoggedIn || !targetAgentId">开始密语</button>
                  <button class="btn btn-danger" @click="stopWhisper" :disabled="!isLoggedIn || !targetAgentId">结束密语</button>
                </div>
              </div>
              
              <!-- 强制状态控制 -->
              <div class="function-group">
                <h3>强制状态控制</h3>
                <div class="button-group">
                  <button class="btn btn-warning" @click="forceBusy" :disabled="!isLoggedIn || !targetAgentId">强制置忙</button>
                  <button class="btn btn-success" @click="forceIdle" :disabled="!isLoggedIn || !targetAgentId">强制置闲</button>
                </div>
              </div>
              
              <!-- 通话拦截 -->
              <div class="function-group">
                <h3>通话拦截</h3>
                <div class="button-group">
                  <button class="btn btn-danger" @click="interceptCall" :disabled="!isLoggedIn || !targetAgentId">拦截通话</button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 操作日志区域 -->
          <div class="operation-logs">
            <h2>操作日志</h2>
            <div class="log-container">
              <div v-if="operationLogs.length === 0" class="no-logs">暂无操作日志</div>
              <div v-else class="log-list">
                <div v-for="(log, index) in operationLogs" :key="index" class="log-item">
                  <span class="log-time">{{ log.time }}</span>
                  <span class="log-type" :class="getLogTypeClass(log.type)">{{ log.type }}</span>
                  <span class="log-content">{{ log.content }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 功能说明 -->
    <div class="feature-description">
      <h2>班长功能说明</h2>
      <div class="feature-list">
        <div class="feature-item">
          <h3>监听功能</h3>
          <p>班长可以监听座席正在进行的通话，但座席和客户听不到班长的声音。</p>
        </div>
        <div class="feature-item">
          <h3>强插功能</h3>
          <p>班长可以加入座席和客户的通话，三方通话，可以和座席、客户都通话。</p>
        </div>
        <div class="feature-item">
          <h3>密语功能</h3>
          <p>班长可以与座席进行私密通话，客户听不到班长和座席之间的对话。</p>
        </div>
        <div class="feature-item">
          <h3>强制状态控制</h3>
          <p>班长可以强制改变座席状态，如将座席设置为忙碌或空闲状态。</p>
        </div>
        <div class="feature-item">
          <h3>通话拦截</h3>
          <p>班长可以拦截座席的通话，让通话转接到班长。</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, defineAsyncComponent } from 'vue';

// 使用异步组件导入CCBarExample
const CCBarExample = defineAsyncComponent(() => import('./CCBarExample.vue'));

import { CCBarService } from '../core/CCBarService';
import GlobalStateManager from '../core/GlobalStateManager';

// 全局状态管理器实例
const globalStateManager = GlobalStateManager.getInstance();

// 状态变量
const targetAgentId = ref('');
const targetCallId = ref('');
const isLoggedIn = ref(globalStateManager.isLoggedIn());
const operationLogs = ref<Array<{ time: string; type: string; content: string }>>([]);
const activeTab = ref('ccbar'); // 默认显示座席控制面板

// 添加日志
const addLog = (type: string, content: string) => {
  const now = new Date();
  const time = `${now.getHours().toString().padStart(2, "0")}:${now
    .getMinutes()
    .toString()
    .padStart(2, "0")}:${now.getSeconds().toString().padStart(2, "0")}`;

  operationLogs.value.unshift({ time, type, content });

  // 限制最多显示50条
  if (operationLogs.value.length > 50) {
    operationLogs.value.pop();
  }
};

// 获取日志类型样式
const getLogTypeClass = (type: string) => {
  switch (type) {
    case '成功':
      return 'log-success';
    case '错误':
      return 'log-error';
    case '警告':
      return 'log-warning';
    default:
      return 'log-info';
  }
};

// 监听功能
const startMonitor = async () => {
  if (!targetAgentId.value) {
    addLog('警告', '请输入目标座席ID');
    return;
  }
  
  addLog('操作', `开始监听座席: ${targetAgentId.value}`);
  try {
    // 获取CCBarService实例
    const ccbarService = CCBarService.getInstance({});
    
    // 确保supervisor属性存在
    if (!ccbarService.supervisor) {
      addLog('错误', '班长操作服务未初始化');
      return;
    }
    
    const result = await ccbarService.supervisor.startMonitor(targetAgentId.value, targetCallId.value || undefined, {});
    
    if (result.state) {
      addLog('成功', `已开始监听座席 ${targetAgentId.value}`);
    } else {
      addLog('错误', `监听失败: ${result.msg}`);
    }
  } catch (error: any) {
    addLog('错误', `监听异常: ${error.message || '未知错误'}`);
  }
};

const stopMonitor = async () => {
  if (!targetAgentId.value) {
    addLog('警告', '请输入目标座席ID');
    return;
  }
  
  addLog('操作', `结束监听座席: ${targetAgentId.value}`);
  try {
    // 获取CCBarService实例
    const ccbarService = CCBarService.getInstance({});
    
    // 确保supervisor属性存在
    if (!ccbarService.supervisor) {
      addLog('错误', '班长操作服务未初始化');
      return;
    }
    
    const result = await ccbarService.supervisor.stopMonitor(targetAgentId.value, targetCallId.value || undefined, {});
    
    if (result.state) {
      addLog('成功', `已结束监听座席 ${targetAgentId.value}`);
    } else {
      addLog('错误', `结束监听失败: ${result.msg}`);
    }
  } catch (error: any) {
    addLog('错误', `结束监听异常: ${error.message || '未知错误'}`);
  }
};

// 强插功能
const startIntercept = async () => {
  if (!targetAgentId.value) {
    addLog('警告', '请输入目标座席ID');
    return;
  }
  
  addLog('操作', `开始强插座席: ${targetAgentId.value}`);
  try {
    // 获取CCBarService实例
    const ccbarService = CCBarService.getInstance({});
    
    // 确保supervisor属性存在
    if (!ccbarService.supervisor) {
      addLog('错误', '班长操作服务未初始化');
      return;
    }
    
    const result = await ccbarService.supervisor.startIntercept(targetAgentId.value, targetCallId.value || undefined, {});
    
    if (result.state) {
      addLog('成功', `已开始强插座席 ${targetAgentId.value}`);
    } else {
      addLog('错误', `强插失败: ${result.msg}`);
    }
  } catch (error: any) {
    addLog('错误', `强插异常: ${error.message || '未知错误'}`);
  }
};

const stopIntercept = async () => {
  if (!targetAgentId.value) {
    addLog('警告', '请输入目标座席ID');
    return;
  }
  
  addLog('操作', `结束强插座席: ${targetAgentId.value}`);
  try {
    // 获取CCBarService实例
    const ccbarService = CCBarService.getInstance({});
    
    // 确保supervisor属性存在
    if (!ccbarService.supervisor) {
      addLog('错误', '班长操作服务未初始化');
      return;
    }
    
    const result = await ccbarService.supervisor.stopIntercept(targetAgentId.value, targetCallId.value || undefined, {});
    
    if (result.state) {
      addLog('成功', `已结束强插座席 ${targetAgentId.value}`);
    } else {
      addLog('错误', `结束强插失败: ${result.msg}`);
    }
  } catch (error: any) {
    addLog('错误', `结束强插异常: ${error.message || '未知错误'}`);
  }
};

// 密语功能
const startWhisper = async () => {
  if (!targetAgentId.value) {
    addLog('警告', '请输入目标座席ID');
    return;
  }
  
  addLog('操作', `开始密语座席: ${targetAgentId.value}`);
  try {
    // 获取CCBarService实例
    const ccbarService = CCBarService.getInstance({});
    
    // 确保supervisor属性存在
    if (!ccbarService.supervisor) {
      addLog('错误', '班长操作服务未初始化');
      return;
    }
    
    const result = await ccbarService.supervisor.startWhisper(targetAgentId.value, targetCallId.value || undefined, {});
    
    if (result.state) {
      addLog('成功', `已开始密语座席 ${targetAgentId.value}`);
    } else {
      addLog('错误', `密语失败: ${result.msg}`);
    }
  } catch (error: any) {
    addLog('错误', `密语异常: ${error.message || '未知错误'}`);
  }
};

const stopWhisper = async () => {
  if (!targetAgentId.value) {
    addLog('警告', '请输入目标座席ID');
    return;
  }
  
  addLog('操作', `结束密语座席: ${targetAgentId.value}`);
  try {
    // 获取CCBarService实例
    const ccbarService = CCBarService.getInstance({});
    
    // 确保supervisor属性存在
    if (!ccbarService.supervisor) {
      addLog('错误', '班长操作服务未初始化');
      return;
    }
    
    const result = await ccbarService.supervisor.stopWhisper(targetAgentId.value, targetCallId.value || undefined, {});
    
    if (result.state) {
      addLog('成功', `已结束密语座席 ${targetAgentId.value}`);
    } else {
      addLog('错误', `结束密语失败: ${result.msg}`);
    }
  } catch (error: any) {
    addLog('错误', `结束密语异常: ${error.message || '未知错误'}`);
  }
};

// 强制状态控制
const forceBusy = async () => {
  if (!targetAgentId.value) {
    addLog('警告', '请输入目标座席ID');
    return;
  }
  
  addLog('操作', `强制置忙座席: ${targetAgentId.value}`);
  try {
    // 获取CCBarService实例
    const ccbarService = CCBarService.getInstance({});
    
    // 确保supervisor属性存在
    if (!ccbarService.supervisor) {
      addLog('错误', '班长操作服务未初始化');
      return;
    }
    
    const result = await ccbarService.supervisor.forceBusy(targetAgentId.value, '班长强制置忙', {});
    
    if (result.state) {
      addLog('成功', `已强制置忙座席 ${targetAgentId.value}`);
    } else {
      addLog('错误', `强制置忙失败: ${result.msg}`);
    }
  } catch (error: any) {
    addLog('错误', `强制置忙异常: ${error.message || '未知错误'}`);
  }
};

const forceIdle = async () => {
  if (!targetAgentId.value) {
    addLog('警告', '请输入目标座席ID');
    return;
  }
  
  addLog('操作', `强制置闲座席: ${targetAgentId.value}`);
  try {
    // 获取CCBarService实例
    const ccbarService = CCBarService.getInstance({});
    
    // 确保supervisor属性存在
    if (!ccbarService.supervisor) {
      addLog('错误', '班长操作服务未初始化');
      return;
    }
    
    const result = await ccbarService.supervisor.forceIdle(targetAgentId.value, {});
    
    if (result.state) {
      addLog('成功', `已强制置闲座席 ${targetAgentId.value}`);
    } else {
      addLog('错误', `强制置闲失败: ${result.msg}`);
    }
  } catch (error: any) {
    addLog('错误', `强制置闲异常: ${error.message || '未知错误'}`);
  }
};

// 通话拦截
const interceptCall = async () => {
  if (!targetAgentId.value) {
    addLog('警告', '请输入目标座席ID');
    return;
  }
  
  addLog('操作', `拦截座席通话: ${targetAgentId.value}`);
  try {
    // 获取CCBarService实例
    const ccbarService = CCBarService.getInstance({});
    
    // 确保supervisor属性存在
    if (!ccbarService.supervisor) {
      addLog('错误', '班长操作服务未初始化');
      return;
    }
    
    const result = await ccbarService.supervisor.interceptCall(targetAgentId.value, targetCallId.value || undefined, {});
    
    if (result.state) {
      addLog('成功', `已拦截座席 ${targetAgentId.value} 的通话`);
    } else {
      addLog('错误', `拦截通话失败: ${result.msg}`);
    }
  } catch (error: any) {
    addLog('错误', `拦截通话异常: ${error.message || '未知错误'}`);
  }
};

// 组件挂载时
onMounted(() => {
  // 监听登录状态变化
  watch(() => globalStateManager.isLoggedIn(), (newVal) => {
    isLoggedIn.value = newVal;
    
    if (newVal) {
      addLog('系统', '已登录，班长操作功能已启用');
    } else {
      addLog('系统', '已登出，班长操作功能已禁用');
    }
  }, { immediate: true });
  
  // 初始化
  addLog('系统', '班长操作组件已加载');
  
  // 如果切换到了班长面板并且已登录，提醒用户可以执行操作
  watch(activeTab, (newTab) => {
    if (newTab === 'supervisor' && isLoggedIn.value) {
      addLog('提示', '可以输入目标座席工号进行班长操作');
    }
  });
  
  // 测试数据 - 可以删除
  targetAgentId.value = '1001'; // 预设一个测试座席ID
});
</script>

<style scoped>
.supervisor-example {
  font-family: Arial, sans-serif;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

h1, h2, h3 {
  color: #303133;
}

h1 {
  text-align: center;
  margin-bottom: 30px;
}

h2 {
  margin-top: 25px;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

h3 {
  font-size: 16px;
  margin-bottom: 10px;
}

/* 标签页样式 */
.tabs-container {
  margin-bottom: 30px;
}

.tabs-header {
  display: flex;
  border-bottom: 2px solid #e4e7ed;
}

.tab {
  padding: 10px 20px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  margin-bottom: -2px;
  font-weight: bold;
  transition: all 0.3s;
}

.tab:hover {
  color: #409eff;
}

.tab.active {
  color: #409eff;
  border-bottom-color: #409eff;
}

.tabs-content {
  padding: 20px 0;
}

.login-status {
  padding: 10px;
  margin-bottom: 20px;
  border-radius: 4px;
  text-align: center;
  font-weight: bold;
}

.login-status.logged-in {
  background-color: #f0f9eb;
  color: #67c23a;
}

.login-status.logged-out {
  background-color: #fef0f0;
  color: #f56c6c;
}

.supervisor-panel {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.target-agent-input {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.input-group {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 200px;
}

.input-group label {
  margin-bottom: 5px;
  font-weight: bold;
  font-size: 14px;
  color: #606266;
}

.input-group input {
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.function-groups {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.function-group {
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  transition: all 0.3s;
}

.function-group:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.button-group {
  display: flex;
  gap: 10px;
}

.btn {
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
  flex: 1;
}

.btn:hover:not(:disabled) {
  opacity: 0.8;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #409eff;
  color: white;
}

.btn-danger {
  background-color: #f56c6c;
  color: white;
}

.btn-warning {
  background-color: #e6a23c;
  color: white;
}

.btn-success {
  background-color: #67c23a;
  color: white;
}

.operation-logs {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
}

.no-logs {
  text-align: center;
  color: #909399;
  padding: 20px 0;
}

.log-list {
  display: flex;
  flex-direction: column;
}

.log-item {
  padding: 8px 0;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  align-items: flex-start;
}

.log-time {
  color: #909399;
  font-size: 12px;
  width: 60px;
}

.log-type {
  padding: 2px 6px;
  border-radius: 10px;
  margin-right: 10px;
  font-size: 12px;
  min-width: 50px;
  text-align: center;
  color: white;
}

.log-info {
  background-color: #909399;
}

.log-success {
  background-color: #67c23a;
}

.log-error {
  background-color: #f56c6c;
}

.log-warning {
  background-color: #e6a23c;
}

.log-content {
  flex: 1;
  font-size: 13px;
  color: #303133;
}

/* 功能说明样式 */
.feature-description {
  margin-top: 30px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.feature-item {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

.feature-item h3 {
  margin-top: 0;
  color: #409eff;
}

.feature-item p {
  color: #606266;
  margin: 0;
  line-height: 1.6;
}

@media screen and (max-width: 768px) {
  .function-groups {
    grid-template-columns: 1fr;
  }
  
  .target-agent-input {
    flex-direction: column;
  }
  
  .feature-list {
    grid-template-columns: 1fr;
  }
}
</style> 