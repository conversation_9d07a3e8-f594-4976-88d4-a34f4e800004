<template>
  <div class="config-page-container">
    <h1 class="title">CCBar 配置管理</h1>

    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>基础配置</span>
          <el-button type="primary" @click="saveConfig">保存配置</el-button>
        </div>
      </template>

      <el-form :model="config" label-width="180px" label-position="left">
        <el-form-item label="API基础URL">
          <el-input v-model="config.baseURL" placeholder="输入API基础URL，例如：/api" />
          <div class="form-tip">用于发送API请求的基础URL，如使用代理则填写 /api</div>
        </el-form-item>

        <el-form-item label="WebSocket URL">
          <el-input v-model="config.wsURL" placeholder="输入WebSocket URL，例如：/ws" />
          <div class="form-tip">用于WebSocket连接的URL，如使用代理则填写 /ws</div>
        </el-form-item>

        <el-form-item label="超时设置(毫秒)">
          <el-input-number v-model="config.timeout" :min="1000" :max="60000" :step="1000" />
          <div class="form-tip">API请求超时时间</div>
        </el-form-item>

        <el-form-item label="轮询间隔(毫秒)">
          <el-input-number v-model="config.pollingInterval" :min="1000" :max="30000" :step="1000" />
          <div class="form-tip">HTTP长轮询间隔时间</div>
        </el-form-item>

        <el-form-item label="心跳间隔(毫秒)">
          <el-input-number v-model="config.heartbeatInterval" :min="5000" :max="60000" :step="1000" />
          <div class="form-tip">WebSocket心跳间隔时间</div>
        </el-form-item>

        <el-form-item label="自动重连">
          <el-switch v-model="config.autoReconnect" />
          <div class="form-tip">WebSocket断开后是否自动重连</div>
        </el-form-item>

        <el-form-item label="最大重连次数">
          <el-input-number v-model="config.maxReconnectAttempts" :min="1" :max="20" :step="1" />
          <div class="form-tip">WebSocket最大重连尝试次数</div>
        </el-form-item>

        <el-form-item label="重连间隔(毫秒)">
          <el-input-number v-model="config.reconnectInterval" :min="1000" :max="10000" :step="1000" />
          <div class="form-tip">WebSocket重连间隔时间</div>
        </el-form-item>

        <el-form-item label="自动就绪">
          <el-switch v-model="config.autoReady" />
          <div class="form-tip">登录成功后是否自动将座席状态设为就绪</div>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>企业与账户配置</span>
        </div>
      </template>

      <el-form :model="config" label-width="180px" label-position="left">
        <el-form-item label="企业ID">
          <el-input v-model="config.entId" placeholder="输入企业ID" />
          <div class="form-tip">企业标识，可选</div>
        </el-form-item>

        <el-form-item label="登录密钥">
          <el-input v-model="config.loginKey" placeholder="输入登录密钥" />
          <div class="form-tip">登录认证密钥，可选</div>
        </el-form-item>

        <el-form-item label="产品ID">
          <el-input v-model="config.productId" placeholder="输入产品ID" />
          <div class="form-tip">产品标识，可选</div>
        </el-form-item>

        <el-form-item label="调试模式">
          <el-switch v-model="config.debug" />
          <div class="form-tip">是否开启调试模式，开启后控制台会输出详细日志</div>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>当前配置预览</span>
          <el-button @click="copyConfig">复制配置</el-button>
        </div>
      </template>

      <pre class="config-preview">{{ JSON.stringify(config, null, 2) }}</pre>
    </el-card>

    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>配置效果预览</span>
        </div>
      </template>

      <div class="effect-preview">
        <p>当前API基础URL: <code>{{ config.baseURL }}</code></p>
        <p>当前WebSocket URL: <code>{{ config.wsURL }}</code></p>
        <p>当前超时设置: <code>{{ config.timeout }}ms</code></p>
        <p>轮询间隔: <code>{{ config.pollingInterval }}ms</code></p>
        <p>心跳间隔: <code>{{ config.heartbeatInterval }}ms</code></p>
        <p>自动重连: <code>{{ config.autoReconnect ? '是' : '否' }}</code></p>
        <p>最大重连次数: <code>{{ config.maxReconnectAttempts }}</code></p>
        <p>重连间隔: <code>{{ config.reconnectInterval }}ms</code></p>
        <p>登录后自动就绪: <code>{{ config.autoReady ? '是' : '否' }}</code></p>
        <p>调试模式: <code>{{ config.debug ? '开启' : '关闭' }}</code></p>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { CCBarConfig } from '../types';
import { loadConfig, saveConfig as saveConfigUtil } from '../utils/config';

// 当前配置对象
const config = reactive<CCBarConfig>(loadConfig());

// 加载已保存的配置
onMounted(() => {
  loadSavedConfig();
});

// 从localStorage加载配置
const loadSavedConfig = () => {
  try {
    const savedConfig = localStorage.getItem('ccbarConfig');
    if (savedConfig) {
      const parsedConfig = JSON.parse(savedConfig);
      // 将保存的配置合并到当前配置
      Object.assign(config, parsedConfig);
      
      // 也设置全局配置，使其在其他页面可访问
      if (typeof window !== 'undefined') {
        window.ccbarConfig = {
          apiBaseUrl: config.baseURL,
          wsUrl: config.wsURL
        };
      }
      
      ElMessage.success('已加载保存的配置');
    }
  } catch (error) {
    console.error('加载配置失败:', error);
    ElMessage.error('加载配置失败');
  }
};

// 保存配置到localStorage
const saveConfig = () => {
  try {
    // 使用工具函数保存配置
    saveConfigUtil(config);
    ElMessage.success('配置已保存并更新全局配置，在其他页面可立即生效');
  } catch (error) {
    console.error('保存配置失败:', error);
    ElMessage.error('保存配置失败');
  }
};

// 复制配置到剪贴板
const copyConfig = async () => {
  try {
    await navigator.clipboard.writeText(JSON.stringify(config, null, 2));
    ElMessage.success('配置已复制到剪贴板');
  } catch (error) {
    console.error('复制配置失败:', error);
    ElMessage.error('复制配置失败');
  }
};

// 声明全局配置类型
declare global {
  interface Window {
    ccbarConfig?: {
      apiBaseUrl?: string;
      wsUrl?: string;
    };
  }
}
</script>

<style scoped>
.config-page-container {
  max-width: 1000px;
  margin: 20px auto;
  padding: 20px;
}

.title {
  text-align: center;
  margin-bottom: 30px;
  color: #409EFF;
}

.config-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.config-preview {
  background-color: #f8f8f8;
  padding: 15px;
  border-radius: 5px;
  overflow: auto;
  max-height: 300px;
}

.effect-preview {
  background-color: #f8f8f8;
  padding: 15px;
  border-radius: 5px;
}

.effect-preview code {
  background-color: #e0e0e0;
  padding: 2px 5px;
  border-radius: 3px;
  font-family: monospace;
}
</style> 