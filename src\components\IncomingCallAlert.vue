<template>
  <div class="call-alert-container" v-if="show">
    <div class="call-alert-box" :class="{ 'flashing-border': show }">
      <div class="call-alert-header">
        <div class="call-alert-title">
          <el-icon><phone-filled /></el-icon>
          <span>来电提醒</span>
        </div>
        <div class="call-alert-timer">{{ timerDisplay }}</div>
      </div>
      
      <div class="call-alert-body">
        <div class="call-info">
          <div class="phone-number">
            <div class="label">来电号码:</div>
            <div class="value">{{ callInfo.phoneNumber }}</div>
          </div>
          
          <div class="customer-info" v-if="callInfo.customerName">
            <div class="label">客户姓名:</div>
            <div class="value">{{ callInfo.customerName }}</div>
          </div>
          
          <div class="call-time">
            <div class="label">呼入时间:</div>
            <div class="value">{{ callInfo.time }}</div>
          </div>
          
          <div class="call-notes" v-if="callInfo.notes">
            <div class="label">备注信息:</div>
            <div class="value">{{ callInfo.notes }}</div>
          </div>
        </div>
      </div>
      
      <div class="call-alert-footer">
        <el-button type="primary" @click="handleAnswer">
          <el-icon><phone /></el-icon> 立即接听
        </el-button>
        <el-button @click="handleReject">
          <el-icon><close /></el-icon> 拒接来电
        </el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { PhoneFilled, Phone, Close } from '@element-plus/icons-vue';

export default defineComponent({
  name: 'incoming-call-alert',
  components: {
    PhoneFilled,
    Phone,
    Close
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    callInfo: {
      type: Object,
      default: () => ({
        phoneNumber: '',
        customerName: '',
        time: '',
        notes: ''
      })
    }
  },
  emits: ['answer', 'reject'],
  setup(props, { emit }) {
    // 计时器
    const ringTimer = ref(0);
    const timerInterval = ref<ReturnType<typeof setInterval> | null>(null);
    const alertAudio = ref<HTMLAudioElement | null>(null);
    
    const timerDisplay = computed(() => {
      const minutes = Math.floor(ringTimer.value / 60);
      const seconds = ringTimer.value % 60;
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    });
    
    // 处理接听
    const handleAnswer = () => {
      stopTimer();
      emit('answer');
    };
    
    // 处理拒接
    const handleReject = () => {
      stopTimer();
      emit('reject');
    };
    
    // 开始计时
    const startTimer = () => {
      ringTimer.value = 0;
      if (timerInterval.value) {
        clearInterval(timerInterval.value);
      }
      
      timerInterval.value = setInterval(() => {
        ringTimer.value++;
      }, 1000);
    };
    
    // 停止计时
    const stopTimer = () => {
      if (timerInterval.value) {
        clearInterval(timerInterval.value);
        timerInterval.value = null;
      }
      
      // 停止音频
      if (alertAudio.value) {
        alertAudio.value.pause();
        alertAudio.value = null;
      }
    };
    
    // 监听显示状态
    watch(() => props.show, (newValue) => {
      if (newValue) {
        startTimer();
        
        try {
          // 尝试播放提示音
          // 在实际应用中这里应该使用真实的铃声文件
          alertAudio.value = new Audio();
          // 使用 base64 编码的音频，确保不需要外部文件
          alertAudio.value.src = 'data:audio/mp3;base64,SUQzAwAAAAAfdlRJVDIAAAAoAAAB//5TAHUAbgBuAHkAIABtAG8AcgBuAGkAbgBnAFQQQUcAAAATAAAA//9SAGkAbgBnAHQAbwBuAGUAVFBFMQAAABUAAAH//5EAaQBuAGcAdABvAG4AZQBz';
          alertAudio.value.loop = true;
          alertAudio.value.play().catch(e => console.warn('无法播放提示音:', e));
        } catch (e) {
          console.error('音频播放失败', e);
        }
      } else {
        stopTimer();
      }
    });
    
    onMounted(() => {
      if (props.show) {
        startTimer();
      }
    });
    
    onUnmounted(() => {
      stopTimer();
    });
    
    return {
      timerDisplay,
      handleAnswer,
      handleReject
    };
  }
});
</script>

<style scoped>
.call-alert-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  backdrop-filter: blur(3px);
}

.call-alert-box {
  width: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  animation: shake 0.5s linear infinite;
}

@keyframes shake {
  0% { transform: translateX(0); }
  25% { transform: translateX(-3px); }
  50% { transform: translateX(0); }
  75% { transform: translateX(3px); }
  100% { transform: translateX(0); }
}

.flashing-border {
  border: 3px solid transparent;
  animation: flashBorder 1s linear infinite;
}

@keyframes flashBorder {
  0% { border-color: rgba(245, 108, 108, 0.4); box-shadow: 0 0 10px rgba(245, 108, 108, 0.4); }
  50% { border-color: rgba(245, 108, 108, 1); box-shadow: 0 0 20px rgba(245, 108, 108, 0.8); }
  100% { border-color: rgba(245, 108, 108, 0.4); box-shadow: 0 0 10px rgba(245, 108, 108, 0.4); }
}

.call-alert-header {
  background: #f56c6c;
  color: white;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.call-alert-title {
  font-size: 18px;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.call-alert-title i {
  margin-right: 8px;
  font-size: 20px;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.call-alert-timer {
  font-size: 16px;
  font-weight: bold;
}

.call-alert-body {
  padding: 20px;
}

.call-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.phone-number, .customer-info, .call-time, .call-notes {
  display: flex;
}

.label {
  width: 80px;
  color: #606266;
  font-weight: bold;
}

.value {
  flex: 1;
  color: #303133;
  font-weight: bold;
}

.phone-number .value {
  font-size: 18px;
  color: #f56c6c;
}

.call-alert-footer {
  padding: 15px;
  display: flex;
  justify-content: center;
  gap: 20px;
  border-top: 1px solid #ebeef5;
}
</style> 