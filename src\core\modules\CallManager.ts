/**
 * 呼叫管理模块
 * 负责管理呼叫操作，如外呼、应答、挂断、保持等
 */

import { ICallManager, IEventManager } from '../interfaces';
import { CCBarResponse } from '../../types';
import { RequestService } from '../../utils/request';
import { ccbarDebugger } from '../../utils';

/**
 * 呼叫管理器实现类
 */
export class CallManager implements ICallManager {
  private requestService: RequestService;
  private eventManager: IEventManager;
  private sessionId: string;
  private agentId: string;
  private currentCallId: string | null = null;

  /**
   * 构造函数
   * @param requestService HTTP请求服务
   * @param eventManager 事件管理器
   * @param sessionId 会话ID
   * @param agentId 座席ID
   */
  constructor(
    requestService: RequestService,
    eventManager: IEventManager,
    sessionId: string,
    agentId: string
  ) {
    this.requestService = requestService;
    this.eventManager = eventManager;
    this.sessionId = sessionId;
    this.agentId = agentId;

    // 监听来电事件，更新当前呼叫ID
    this.eventManager.on('call:incoming', (data: any) => {
      if (data && data.callId) {
        this.currentCallId = data.callId;
      }
    });

    // 监听通话结束事件，清空当前呼叫ID
    this.eventManager.on('call:ended', () => {
      this.currentCallId = null;
    });
  }

  /**
   * 更新会话ID
   * @param sessionId 新的会话ID
   */
  public updateSessionId(sessionId: string): void {
    this.sessionId = sessionId;
  }

  /**
   * 发起呼叫
   * @param phoneNumber 呼叫的电话号码
   * @param displayNumber 外显号码
   * @returns 呼叫结果
   */
  public async makeCall(phoneNumber: string, displayNumber?: string): Promise<CCBarResponse> {

    try {
      // 构建外呼请求参数
      // callType 
      const callParams = {
        data: {
          agentId: this.agentId || localStorage.getItem('agentId') || '',
          caller: displayNumber || '',
          called: phoneNumber,
          userData: {},
          callType: 2,
          skillId: '',
          timestamp: new Date().getTime(),
          cuid: localStorage.getItem('cuid') || ''
        }

      };

      // 发送外呼请求
      const result = await this.requestService.post('/CallEvent?action=Makecall', callParams, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8'
        }
      });

      // 处理响应结果
      if (result.state) {
        // 如果响应中包含呼叫ID，则保存
        if (result.data.result && result.data.result.callId) {
          this.currentCallId = result.data.result.callId;
        }

        // 触发外呼事件
        this.eventManager.emit('call:dialing', {
          phoneNumber,
          displayNumber,
          timestamp: new Date().getTime()
        });

        return {
          state: true,
          msg: '外呼请求已发送',
          data: {
            code: 'succ',
            content: '外呼请求已发送',
            result: result.msg
          }
        };
      } else {
        return {
          state: false,
          msg: result.data?.content || '外呼失败',
          data: {
            code: 'fail',
            content: result.data?.content || '外呼失败',
            result: null
          }
        };
      }
    } catch (error: any) {
      ccbarDebugger('外呼异常', error);
      return {
        state: false,
        msg: error.message || '外呼异常',
        data: {
          code: 'error',
          content: error.message || '外呼异常',
          result: null
        }
      };
    }
  }

  /**
   * 应答呼叫
   * @param callId 呼叫ID，不传则使用当前呼叫
   * @returns 应答结果
   */
  public async answerCall(callId?: string): Promise<CCBarResponse> {
    const targetCallId = callId || this.currentCallId;

    if (!this.sessionId) {
      return {
        state: false,
        msg: '会话ID不存在，请先登录',
        data: {
          code: 'fail',
          content: '会话ID不存在，请先登录',
          result: null
        }
      };
    }

    if (!targetCallId) {
      return {
        state: false,
        msg: '没有需要应答的呼叫',
        data: {
          code: 'fail',
          content: '没有需要应答的呼叫',
          result: null
        }
      };
    }

    try {
      // 构建应答请求参数
      const answerParams = {
        cmdJson: JSON.stringify({
          cmd: 'answerCall',
          agentId: this.agentId,
          sessionId: this.sessionId,
          callId: targetCallId,
          event: 'answerCall',
          timestamp: new Date().getTime()
        })
      };

      // 发送应答请求
      const result = await this.requestService.post('/AgentEvent?action=event', answerParams);

      // 处理响应结果
      if (result.state) {
        // 触发应答事件
        this.eventManager.emit('call:answered', {
          callId: targetCallId,
          timestamp: new Date().getTime()
        });

        return {
          state: true,
          msg: '呼叫已应答',
          data: {
            code: 'succ',
            content: '呼叫已应答',
            result: result.msg
          }
        };
      } else {
        return {
          state: false,
          msg: result.data?.content || '应答失败',
          data: {
            code: 'fail',
            content: result.data?.content || '应答失败',
            result: null
          }
        };
      }
    } catch (error: any) {
      ccbarDebugger('应答异常', error);
      return {
        state: false,
        msg: error.message || '应答异常',
        data: {
          code: 'error',
          content: error.message || '应答异常',
          result: null
        }
      };
    }
  }
  /**
 * 麦克风静音
 * @param callId 呼叫ID，不传则使用当前呼叫
 * @returns 静音结果
 */
  public async muteCall(callId?: string): Promise<CCBarResponse> {
    const targetCallId = callId || this.getCurrentCallId();


    try {
      // 构建静音请求参数
      const muteParams = {
        data: {
          messageId: 'cmdMuteCall',
          cuid: localStorage.getItem('cuid') || '',
          timestamp: new Date().getTime()
        }
      };

      // 发送静音请求
      const result = await this.requestService.post('/AgentEvent?action=event', muteParams, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8'
        }
      });
      // 处理响应结果
      if (result.state) {
        // 触发静音事件
        this.eventManager.emit('call:muted', {
          callId: targetCallId,
          timestamp: new Date().getTime()
        });

        return {
          state: true,
          msg: '麦克风已静音',
          data: {
            code: 'succ',
            content: '麦克风已静音',
            result: result.msg
          }
        };
      } else {
        return {
          state: false,
          msg: result.data?.content || '静音失败',
          data: {
            code: 'fail',
            content: result.data?.content || '静音失败',
            result: null
          }
        };
      }
    } catch (error: any) {
      ccbarDebugger('静音异常', error);
      return {
        state: false,
        msg: error.message || '静音操作失败',
        data: {
          code: 'error',
          content: error.message || '静音操作失败'
        }
      };
    }
  }

  /**
   * 取消麦克风静音
   * @param callId 呼叫ID，不传则使用当前呼叫
   * @returns 取消静音结果
   */
  public async unmuteCall(callId?: string): Promise<CCBarResponse> {
    const targetCallId = callId || this.getCurrentCallId();

    try {
      // 构建取消静音请求参数
      const unmuteParams = {
        data: {
          messageId: 'cmdRetrieveCall',
          cuid: localStorage.getItem('cuid') || '',
          timestamp: new Date().getTime(),
          type: 12,
        }
      };

      // 发送取消静音请求
      const result = await this.requestService.post('/AgentEvent?action=event', unmuteParams, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8'
        }
      });

      // 处理响应结果
      if (result.state) {
        // 触发取消静音事件
        this.eventManager.emit('call:unmuted', {
          callId: targetCallId,
          timestamp: new Date().getTime()
        });

        return {
          state: true,
          msg: '麦克风已取消静音',
          data: {
            code: 'succ',
            content: '麦克风已取消静音',
            result: result.msg
          }
        };
      } else {
        return {
          state: false,
          msg: result.data?.content || '取消静音失败',
          data: {
            code: 'fail',
            content: result.data?.content || '取消静音失败',
            result: null
          }
        };
      }
    } catch (error: any) {
      ccbarDebugger('取消静音异常', error);
      return {
        state: false,
        msg: error.message || '取消静音操作失败',
        data: {
          code: 'error',
          content: error.message || '取消静音操作失败'
        }
      };
    }
  }

  /**
   * 挂断呼叫
   * @param callId 呼叫ID，不传则使用当前呼叫
   * @returns 挂断结果
   */
  public async hangupCall(callData?: any): Promise<CCBarResponse> {
    const targetCallId = callData || this.currentCallId;
    const callParams = {
      data: {
        agentId: this.agentId || localStorage.getItem('agentId') || '',
        caller: callData.caller || '114',
        called: callData.called || '',
        callId: callData.callId || '',
        messageId: 'cmdClearCall',
        timestamp: new Date().getTime(),
        cuid: localStorage.getItem('cuid') || ''
      }
    }
    // 发送挂断请求
    const result = await this.requestService.post('/AgentEvent?action=event', callParams, {
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      }
    });

    try {
      // 处理响应结果
      if (result.state) {
        // 清空当前呼叫ID
        if (targetCallId === this.currentCallId) {
          this.currentCallId = null;
        }

        // 触发挂断事件
        this.eventManager.emit('call:hangup', {
          callId: targetCallId,
          timestamp: new Date().getTime()
        });

        return {
          state: true,
          msg: '呼叫已挂断',
          data: {
            code: 'succ',
            content: '呼叫已挂断',
            result: result.msg
          }
        };
      } else {
        return {
          state: false,
          msg: result.data?.content || '挂断失败',
          data: {
            code: 'fail',
            content: result.data?.content || '挂断失败',
            result: null
          }
        };
      }
    } catch (error: any) {
      ccbarDebugger('挂断异常', error);
      return {
        state: false,
        msg: error.message || '挂断异常',
        data: {
          code: 'error',
          content: error.message || '挂断异常',
          result: null
        }
      };
    }
  }

  /**
   * 保持呼叫
   * @param callId 呼叫ID，不传则使用当前呼叫
   * @returns 保持结果
   */
  public async holdCall(callId?: string): Promise<CCBarResponse> {
    const targetCallId = callId || this.currentCallId;

    try {
      // 构建保持请求参数
      const holdParams = {
        data: {
          messageId: 'cmdHoldCall',
          cuid: localStorage.getItem('cuid') || '',
          timestamp: new Date().getTime()
        }
      };

      // 发送保持请求
      const result = await this.requestService.post('/AgentEvent?action=event', holdParams, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8'
        }
      });
      // 处理响应结果
      if (result.state) {
        // 触发保持事件
        this.eventManager.emit('call:held', {
          callId: targetCallId,
          timestamp: new Date().getTime()
        });
        return {
          state: true,
          msg: '呼叫已保持',
          data: {
            code: 'succ',
            content: '呼叫已保持',
            result: result.msg
          }
        };
      } else {
        return {
          state: false,
          msg: result.data?.content || '保持失败',
          data: {
            code: 'fail',
            content: result.data?.content || '保持失败',
            result: null
          }
        };
      }
    } catch (error: any) {
      ccbarDebugger('保持异常', error);
      return {
        state: false,
        msg: error.message || '保持异常',
        data: {
          code: 'error',
          content: error.message || '保持异常',
          result: null
        }
      };
    }
  }

  /**
   * 取回呼叫
   * @param callId 呼叫ID，不传则使用当前呼叫
   * @returns 取回结果
   */
  public async retrieveCall(callId?: string): Promise<CCBarResponse> {
    const targetCallId = callId || this.currentCallId;

    try {
      // 构建取回请求参数
      const retrieveParams = {
        data: {
          messageId: 'cmdRetrieveCall',
          type: 11,
          cuid: localStorage.getItem('cuid') || '',
          timestamp: new Date().getTime()
        }
      };

      // 发送取回请求
      const result = await this.requestService.post('/AgentEvent?action=event', retrieveParams, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8'
        }
      });

      // 处理响应结果
      if (result.state) {
        // 触发取回事件
        this.eventManager.emit('call:retrieved', {
          callId: targetCallId,
          timestamp: new Date().getTime()
        });

        return {
          state: true,
          msg: '呼叫已取回',
          data: {
            code: 'succ',
            content: '呼叫已取回',
            result: result.msg
          }
        };
      } else {
        return {
          state: false,
          msg: result.data?.content || '取回失败',
          data: {
            code: 'fail',
            content: result.data?.content || '取回失败',
            result: null
          }
        };
      }
    } catch (error: any) {
      ccbarDebugger('取回异常', error);
      return {
        state: false,
        msg: error.message || '取回异常',
        data: {
          code: 'error',
          content: error.message || '取回异常',
          result: null
        }
      };
    }
  }

  /**
   * 获取当前呼叫ID
   * @returns 当前呼叫ID，如果没有则返回null
   */
  public getCurrentCallId(): string | null {
    return this.currentCallId;
  }

  /**
   * 设置当前呼叫ID
   * @param callId 呼叫ID
   */
  public setCurrentCallId(callId: string): void {
    this.currentCallId = callId;
  }

  /**
   * 清空当前呼叫ID
   */
  public clearCurrentCallId(): void {
    this.currentCallId = null;
  }

  /**
   * 挂断电话 - CCBarService 接口兼容方法
   * @param callId 呼叫ID，默认使用当前呼叫ID
   * @returns 操作结果
   */
  public async clearCall(callData?: string): Promise<CCBarResponse> {
    return this.hangupCall(callData);
  }

  /**
   * 恢复通话 - CCBarService 接口兼容方法
   * @param callId 呼叫ID，默认使用当前呼叫ID
   * @returns 操作结果
   */
  public async unholdCall(callId?: string): Promise<CCBarResponse> {
    return this.retrieveCall(callId);
  }

  /**
   * 获取当前呼叫信息 - CCBarService 接口兼容方法
   * @returns 呼叫信息
   */
  public getCurrentCall(): any | null {
    const callId = this.getCurrentCallId();
    if (!callId) return null;

    return {
      callId,
      // 返回基本呼叫信息
      state: 'active', // 可能的值: 'alerting', 'active', 'held', 'disconnected'
      timestamp: new Date().getTime(),
      isIncoming: false
    };
  }


} 