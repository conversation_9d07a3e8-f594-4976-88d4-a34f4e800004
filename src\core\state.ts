/**
 * 全局状态管理模块
 * 这个文件导出全局状态管理器的单例实例，
 * 可以被任何需要访问全局状态的模块导入使用
 */

import GlobalStateManager from './GlobalStateManager';

// 导出全局状态管理器单例
export const globalState = GlobalStateManager.getInstance();

// 导出一些有用的工具函数
export const getAgentState = () => globalState.getState();
export const getStateType = () => globalState.getStateType();
export const getAgentId = () => globalState.getAgentId();
export const isLoggedIn = () => globalState.isLoggedIn();
export const checkFuncEnabled = (func: any) => globalState.isFuncEnabled(func);

// 状态历史记录
export const getStateHistory = () => globalState.getStateHistory();

// 导出完整的GlobalStateManager类，以防需要完整访问
export default GlobalStateManager; 