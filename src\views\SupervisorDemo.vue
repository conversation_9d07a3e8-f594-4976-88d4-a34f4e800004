<template>
  <div class="supervisor-demo">
    <h1>班长功能演示</h1>
    
    <div class="control-panel">
      <div class="input-group">
        <label for="agentId">目标座席ID：</label>
        <input id="agentId" v-model="targetAgentId" placeholder="请输入座席ID" />
      </div>
      
      <div class="input-group">
        <label for="callId">通话ID：</label>
        <input id="callId" v-model="targetCallId" placeholder="可选，留空使用当前通话" />
      </div>
    </div>
    
    <div class="action-panels">
      <div class="panel">
        <h2>监听功能</h2>
        <div class="button-group">
          <button @click="startMonitor" class="primary">开始监听</button>
          <button @click="stopMonitor" class="warning">结束监听</button>
        </div>
      </div>
      
      <div class="panel">
        <h2>强插功能</h2>
        <div class="button-group">
          <button @click="startIntercept" class="primary">开始强插</button>
          <button @click="stopIntercept" class="warning">结束强插</button>
        </div>
      </div>
      
      <div class="panel">
        <h2>密语功能</h2>
        <div class="button-group">
          <button @click="startWhisper" class="primary">开始密语</button>
          <button @click="stopWhisper" class="warning">结束密语</button>
        </div>
      </div>
      
      <div class="panel">
        <h2>状态控制</h2>
        <div class="button-group">
          <button @click="forceBusy" class="danger">强制置忙</button>
          <button @click="forceIdle" class="success">强制置闲</button>
          <button @click="forceLogout" class="danger">强制签出</button>
        </div>
      </div>
      
      <div class="panel">
        <h2>其他操作</h2>
        <div class="button-group">
          <button @click="interceptCall" class="danger">拦截通话</button>
        </div>
      </div>
    </div>
    
    <div class="log-panel">
      <h2>操作日志</h2>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" :class="getLogClass(log.type)">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-type">[{{ log.type }}]</span>
          <span class="log-content">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { CCBarService } from '../core/CCBarService';

// 状态和参数
const targetAgentId = ref('');
const targetCallId = ref('');
const logs = ref<{ type: string; time: string; message: string }[]>([]);
const isLoggedIn = ref(false);

// 添加日志的辅助函数
const addLog = (type: string, message: string, id: string = '') => {
  const now = new Date();
  const timeStr = now.toLocaleTimeString();
  
  // 构建日志内容，如果提供了ID则添加
  const logMessage = id ? `[ID:${id}] ${message}` : message;
  
  logs.value.unshift({
    type,
    time: timeStr,
    message: logMessage
  });
  
  // 最多保留100条日志
  if (logs.value.length > 100) {
    logs.value.pop();
  }
};

// 获取日志CSS类名
const getLogClass = (type: string) => {
  switch (type) {
    case '成功':
      return 'log-success';
    case '错误':
      return 'log-error';
    case '警告':
      return 'log-warning';
    case '操作':
      return 'log-info';
    default:
      return 'log-info';
  }
};

// 监听功能
const startMonitor = async () => {
  if (!targetAgentId.value) {
    addLog('警告', '请输入目标座席ID', 'MONITOR_NO_AGENT');
    return;
  }
  
  addLog('操作', `开始监听座席: ${targetAgentId.value}`, 'MONITOR_START');
  try {
    // 获取CCBarService实例
    const ccbarService = CCBarService.getInstance({});
    
    // 确保supervisor属性存在
    if (!ccbarService.supervisor) {
      addLog('错误', '班长操作服务未初始化', 'SUPERVISOR_NOT_INIT');
      return;
    }
    
    const result = await ccbarService.supervisor.startMonitor(targetAgentId.value, targetCallId.value || undefined, {});
    
    if (result.state) {
      addLog('成功', `已开始监听座席 ${targetAgentId.value}`, 'MONITOR_SUCCESS');
    } else {
      addLog('错误', `监听失败: ${result.msg}`, 'MONITOR_FAILED');
    }
  } catch (error: any) {
    addLog('错误', `监听异常: ${error.message || '未知错误'}`, 'MONITOR_ERROR');
  }
};

const stopMonitor = async () => {
  if (!targetAgentId.value) {
    addLog('警告', '请输入目标座席ID', 'STOP_MONITOR_NO_AGENT');
    return;
  }
  
  addLog('操作', `结束监听座席: ${targetAgentId.value}`, 'STOP_MONITOR_START');
  try {
    // 获取CCBarService实例
    const ccbarService = CCBarService.getInstance({});
    
    // 确保supervisor属性存在
    if (!ccbarService.supervisor) {
      addLog('错误', '班长操作服务未初始化', 'SUPERVISOR_NOT_INIT');
      return;
    }
    
    const result = await ccbarService.supervisor.stopMonitor(targetAgentId.value, targetCallId.value || undefined, {});
    
    if (result.state) {
      addLog('成功', `已结束监听座席 ${targetAgentId.value}`, 'STOP_MONITOR_SUCCESS');
    } else {
      addLog('错误', `结束监听失败: ${result.msg}`, 'STOP_MONITOR_FAILED');
    }
  } catch (error: any) {
    addLog('错误', `结束监听异常: ${error.message || '未知错误'}`, 'STOP_MONITOR_ERROR');
  }
};

// 强插功能
const startIntercept = async () => {
  if (!targetAgentId.value) {
    addLog('警告', '请输入目标座席ID');
    return;
  }
  
  addLog('操作', `开始强插座席: ${targetAgentId.value}`);
  try {
    // 获取CCBarService实例
    const ccbarService = CCBarService.getInstance({});
    
    // 确保supervisor属性存在
    if (!ccbarService.supervisor) {
      addLog('错误', '班长操作服务未初始化');
      return;
    }
    
    const result = await ccbarService.supervisor.startIntercept(targetAgentId.value, targetCallId.value || undefined, {});
    
    if (result.state) {
      addLog('成功', `已开始强插座席 ${targetAgentId.value}`);
    } else {
      addLog('错误', `强插失败: ${result.msg}`);
    }
  } catch (error: any) {
    addLog('错误', `强插异常: ${error.message || '未知错误'}`);
  }
};

const stopIntercept = async () => {
  if (!targetAgentId.value) {
    addLog('警告', '请输入目标座席ID');
    return;
  }
  
  addLog('操作', `结束强插座席: ${targetAgentId.value}`);
  try {
    // 获取CCBarService实例
    const ccbarService = CCBarService.getInstance({});
    
    // 确保supervisor属性存在
    if (!ccbarService.supervisor) {
      addLog('错误', '班长操作服务未初始化');
      return;
    }
    
    const result = await ccbarService.supervisor.stopIntercept(targetAgentId.value, targetCallId.value || undefined, {});
    
    if (result.state) {
      addLog('成功', `已结束强插座席 ${targetAgentId.value}`);
    } else {
      addLog('错误', `结束强插失败: ${result.msg}`);
    }
  } catch (error: any) {
    addLog('错误', `结束强插异常: ${error.message || '未知错误'}`);
  }
};

// 密语功能
const startWhisper = async () => {
  if (!targetAgentId.value) {
    addLog('警告', '请输入目标座席ID');
    return;
  }
  
  addLog('操作', `开始密语座席: ${targetAgentId.value}`);
  try {
    // 获取CCBarService实例
    const ccbarService = CCBarService.getInstance({});
    
    // 确保supervisor属性存在
    if (!ccbarService.supervisor) {
      addLog('错误', '班长操作服务未初始化');
      return;
    }
    
    const result = await ccbarService.supervisor.startWhisper(targetAgentId.value, targetCallId.value || undefined, {});
    
    if (result.state) {
      addLog('成功', `已开始密语座席 ${targetAgentId.value}`);
    } else {
      addLog('错误', `密语失败: ${result.msg}`);
    }
  } catch (error: any) {
    addLog('错误', `密语异常: ${error.message || '未知错误'}`);
  }
};

const stopWhisper = async () => {
  if (!targetAgentId.value) {
    addLog('警告', '请输入目标座席ID');
    return;
  }
  
  addLog('操作', `结束密语座席: ${targetAgentId.value}`);
  try {
    // 获取CCBarService实例
    const ccbarService = CCBarService.getInstance({});
    
    // 确保supervisor属性存在
    if (!ccbarService.supervisor) {
      addLog('错误', '班长操作服务未初始化');
      return;
    }
    
    const result = await ccbarService.supervisor.stopWhisper(targetAgentId.value, targetCallId.value || undefined, {});
    
    if (result.state) {
      addLog('成功', `已结束密语座席 ${targetAgentId.value}`);
    } else {
      addLog('错误', `结束密语失败: ${result.msg}`);
    }
  } catch (error: any) {
    addLog('错误', `结束密语异常: ${error.message || '未知错误'}`);
  }
};

// 强制状态控制
const forceBusy = async () => {
  if (!targetAgentId.value) {
    addLog('警告', '请输入目标座席ID');
    return;
  }
  
  addLog('操作', `强制置忙座席: ${targetAgentId.value}`);
  try {
    // 获取CCBarService实例
    const ccbarService = CCBarService.getInstance({});
    
    // 确保supervisor属性存在
    if (!ccbarService.supervisor) {
      addLog('错误', '班长操作服务未初始化');
      return;
    }
    
    const result = await ccbarService.supervisor.forceBusy(targetAgentId.value, '班长强制置忙', {});
    
    if (result.state) {
      addLog('成功', `已强制置忙座席 ${targetAgentId.value}`);
    } else {
      addLog('错误', `强制置忙失败: ${result.msg}`);
    }
  } catch (error: any) {
    addLog('错误', `强制置忙异常: ${error.message || '未知错误'}`);
  }
};

const forceIdle = async () => {
  if (!targetAgentId.value) {
    addLog('警告', '请输入目标座席ID');
    return;
  }
  
  addLog('操作', `强制置闲座席: ${targetAgentId.value}`);
  try {
    // 获取CCBarService实例
    const ccbarService = CCBarService.getInstance({});
    
    // 确保supervisor属性存在
    if (!ccbarService.supervisor) {
      addLog('错误', '班长操作服务未初始化');
      return;
    }
    
    const result = await ccbarService.supervisor.forceIdle(targetAgentId.value, {});
    
    if (result.state) {
      addLog('成功', `已强制置闲座席 ${targetAgentId.value}`);
    } else {
      addLog('错误', `强制置闲失败: ${result.msg}`);
    }
  } catch (error: any) {
    addLog('错误', `强制置闲异常: ${error.message || '未知错误'}`);
  }
};

// 强制签出
const forceLogout = async () => {
  if (!targetAgentId.value) {
    addLog('警告', '请输入目标座席ID');
    return;
  }
  
  addLog('操作', `强制签出座席: ${targetAgentId.value}`);
  try {
    // 获取CCBarService实例
    const ccbarService = CCBarService.getInstance({});
    
    // 确保supervisor属性存在
    if (!ccbarService.supervisor) {
      addLog('错误', '班长操作服务未初始化');
      return;
    }
    
    const result = await ccbarService.supervisor.forceLogout(targetAgentId.value, '班长强制签出', {});
    
    if (result.state) {
      addLog('成功', `已强制签出座席 ${targetAgentId.value}`);
    } else {
      addLog('错误', `强制签出失败: ${result.msg}`);
    }
  } catch (error: any) {
    addLog('错误', `强制签出异常: ${error.message || '未知错误'}`);
  }
};

// 拦截通话
const interceptCall = async () => {
  if (!targetAgentId.value) {
    addLog('警告', '请输入目标座席ID');
    return;
  }
  
  addLog('操作', `拦截通话: ${targetAgentId.value}`);
  try {
    // 获取CCBarService实例
    const ccbarService = CCBarService.getInstance({});
    
    // 确保supervisor属性存在
    if (!ccbarService.supervisor) {
      addLog('错误', '班长操作服务未初始化');
      return;
    }
    
    const result = await ccbarService.supervisor.interceptCall(targetAgentId.value, targetCallId.value || undefined, {});
    
    if (result.state) {
      addLog('成功', `已拦截座席 ${targetAgentId.value} 的通话`);
    } else {
      addLog('错误', `拦截通话失败: ${result.msg}`);
    }
  } catch (error: any) {
    addLog('错误', `拦截通话异常: ${error.message || '未知错误'}`);
  }
};

// 初始化
onMounted(() => {
  // 尝试初始化并登录
  const initSystem = async () => {
    try {
      addLog('操作', '正在初始化班长系统...');
      const ccbarService = CCBarService.getInstance({
        baseURL: 'http://localhost:8080',
        wsURL: 'ws://localhost:8081'
      });
      
      // 添加事件监听
      ccbarService.on('supervisor:monitorStarted', (data: any) => {
        addLog('成功', `监听开始事件: ${JSON.stringify(data)}`);
      });
      
      ccbarService.on('supervisor:monitorStopped', (data: any) => {
        addLog('成功', `监听结束事件: ${JSON.stringify(data)}`);
      });
      
      ccbarService.on('supervisor:forceCallStarted', (data: any) => {
        addLog('成功', `强插开始事件: ${JSON.stringify(data)}`);
      });
      
      ccbarService.on('supervisor:forceCallStopped', (data: any) => {
        addLog('成功', `强插结束事件: ${JSON.stringify(data)}`);
      });
      
      ccbarService.on('supervisor:whisperStarted', (data: any) => {
        addLog('成功', `密语开始事件: ${JSON.stringify(data)}`);
      });
      
      ccbarService.on('supervisor:whisperStopped', (data: any) => {
        addLog('成功', `密语结束事件: ${JSON.stringify(data)}`);
      });
      
      ccbarService.on('supervisor:forceLogout', (data: any) => {
        addLog('成功', `强制签出事件: ${JSON.stringify(data)}`);
      });
      
      addLog('成功', '班长系统初始化完成');
      
      // 设置模拟的班长ID
      targetAgentId.value = 'agent1002';
    } catch (error: any) {
      addLog('错误', `初始化失败: ${error.message || '未知错误'}`);
    }
  };
  
  initSystem();
});
</script>

<style scoped>
.supervisor-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

h1 {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}

.control-panel {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.input-group {
  flex: 1;
}

.input-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
}

.input-group input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.action-panels {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.panel {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.panel h2 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 18px;
  color: #333;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

button {
  padding: 10px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
  color: white;
  font-weight: bold;
}

button.primary {
  background-color: #1976d2;
}

button.primary:hover {
  background-color: #1565c0;
}

button.warning {
  background-color: #ff9800;
}

button.warning:hover {
  background-color: #f57c00;
}

button.danger {
  background-color: #f44336;
}

button.danger:hover {
  background-color: #e53935;
}

button.success {
  background-color: #4caf50;
}

button.success:hover {
  background-color: #43a047;
}

.log-panel {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 20px;
}

.log-panel h2 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 18px;
  color: #333;
}

.log-container {
  background-color: #1e1e1e;
  border-radius: 4px;
  padding: 10px;
  height: 300px;
  overflow-y: auto;
  color: #eee;
  font-family: 'Courier New', monospace;
  font-size: 14px;
}

.log-container div {
  padding: 4px 0;
  border-bottom: 1px solid #333;
}

.log-container .log-time {
  color: #888;
  margin-right: 10px;
}

.log-container .log-type {
  margin-right: 10px;
}

.log-container .log-content {
  word-break: break-all;
}

.log-container .log-success .log-type {
  color: #4caf50;
}

.log-container .log-error .log-type {
  color: #f44336;
}

.log-container .log-warning .log-type {
  color: #ff9800;
}

.log-container .log-info .log-type {
  color: #2196f3;
}
</style> 