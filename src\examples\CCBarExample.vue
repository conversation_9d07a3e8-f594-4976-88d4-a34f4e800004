<!--
CCBarExample.vue - CCBar组件使用示例

这个示例展示了如何在第三方应用中集成CCBar话务条和登录组件：
1. 配置CCBar参数
2. 引入并使用话务条组件
3. 处理登录和状态变更事件
4. 集成登录对话框
-->

<template>
  <div class="ccbar-example">
    <h1>CCBar 集成示例</h1>

    <!-- 话务条组件 -->
    <cc-bar
      :config="ccbarConfig"
      @state-changed="handleStateChange"
      @login="handleLogin"
      @logout="handleLogout"
      @call-connected="handleCallEvent"
      @call-disconnected="handleCallEvent"
    ></cc-bar>

    <!-- 示例内容 -->
    <div class="example-content">
      <div class="status-card">
        <h3>当前状态</h3>
        <div class="status-item">
          <span class="status-label">登录状态:</span>
          <span class="status-value">{{
            isLoggedIn ? "已登录" : "未登录"
          }}</span>
        </div>
        <div class="status-item" v-if="isLoggedIn">
          <span class="status-label">工号:</span>
          <span class="status-value">{{ agentInfo.agentId }}</span>
        </div>
        <div class="status-item" v-if="isLoggedIn">
          <span class="status-label">话机:</span>
          <span class="status-value">{{ agentInfo.phone }}</span>
        </div>
        <div class="status-item" v-if="isLoggedIn">
          <span class="status-label">座席状态:</span>
          <span class="status-value">{{ agentStatus.stateDesc }}</span>
        </div>
      </div>

      <div class="event-card">
        <h3>事件日志</h3>
        <div class="event-list">
          <div v-if="eventLogs.length === 0" class="no-events">暂无事件</div>
          <div v-else>
            <div
              v-for="(event, index) in eventLogs"
              :key="index"
              class="event-item"
            >
              <div class="event-time">{{ event.time }}</div>
              <div class="event-type">{{ event.type }}</div>
              <div class="event-content">{{ event.content }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 添加功能说明 -->
      <div class="feature-card">
        <h3>功能说明</h3>
        <div class="feature-list">
          <div class="feature-item">
            <div class="feature-title">座席状态</div>
            <div class="feature-desc">
              话务条显示当前座席状态（置闲、置忙、通话中等）和状态持续时间
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-title">呼叫控制</div>
            <div class="feature-desc">
              提供拨号、挂断、保持、恢复等基本呼叫控制功能
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-title">状态控制</div>
            <div class="feature-desc">
              提供置闲、置忙、小休等座席状态控制功能
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-title">折叠/展开</div>
            <div class="feature-desc">话务条可折叠/展开以节省界面空间</div>
          </div>
        </div>
      </div>
    </div>

    <div class="integration-help">
      <h3>集成方法</h3>
      <code>
        <pre>
          // 1. 引入组件
          import CCBarToolbar from '@/components/CCBarToolbar.vue';
          import CCBarLogin from '@/components/CCBarLogin.vue';

          // 2. 配置CCBar参数
          const ccbarConfig = {
            baseURL: '/api',  // API基础地址  
            wsURL: '/ws',     // WebSocket地址
            entId: '123456',  // 企业ID
            loginKey: 'xxxxxx', // 登录密钥
            productId: '100000' // 产品ID
          };

          // 3. 处理事件
          const handleLoginSuccess = (data) => {
            // 登录成功处理
          };

          const handleStateChanged = (state) => {
            // 状态变更处理
          };

          const handleCallEvent = (event) => {
            // 呼叫事件处理
          };
        </pre>
      </code>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  onMounted,
  onBeforeUnmount,
  watchEffect,
  defineAsyncComponent,
} from "vue";

// 使用defineAsyncComponent异步导入组件
const CCBar = defineAsyncComponent(() => import("../components/CCBar.vue"));
const CCBarLogin = defineAsyncComponent(
  () => import("../components/CCBarLogin.vue")
);

import GlobalStateManager from "../core/GlobalStateManager";
import { AgentStateType } from "../types";
import { EventEmitter } from "../core/EventEmitter";

// CCBar配置
const ccbarConfig = {
  baseURL: "/api/yc-ccbar-v1", // API基础地址
  wsURL: "/ws", // WebSocket地址
  entId: "123456", // 企业ID
  loginKey: "abcdef1234567890abcdef1234567890", // 登录密钥
  productId: "100000", // 产品ID
  debug: true, // 开启调试模式
};

// 全局状态管理器
const globalStateManager = GlobalStateManager.getInstance(ccbarConfig);

// 状态变量
const showLoginDialog = ref(false);
const isLoggedIn = ref(globalStateManager.isLoggedIn());
const skills = ref<any[]>([]);
const agentInfo = reactive({
  agentId: "",
  phone: "",
});
const agentStatus = reactive({
  state: AgentStateType.LOGOFF,
  stateDesc: "未登录",
  funcMask: {},
});
const eventLogs = ref<Array<{ time: string; type: string; content: string }>>(
  []
);

// 定时器引用
let stateCheckInterval: ReturnType<typeof setInterval> | null = null;

// 处理登录成功
const handleLogin = (data: any) => {
  console.log('收到登录成功事件:', data);
  
  // 更新登录状态
  isLoggedIn.value = true;
  
  // 更新座席信息
  agentInfo.agentId = data.agentId;
  agentInfo.phone = data.phone || data.phoneNumber || '';
  
  // 如果有状态信息，更新座席状态
  if (data.state) {
    agentStatus.state = data.state as AgentStateType;
    agentStatus.stateDesc = data.stateDesc || getStateDescription(data.state);
    if (data.funcMask) {
      agentStatus.funcMask = { ...data.funcMask };
    }
  } else {
    // 否则获取全局状态
    const currentState = globalStateManager.getState();
    if (currentState) {
      agentStatus.state = currentState.state as AgentStateType;
      agentStatus.stateDesc = currentState.stateDesc;
      agentStatus.funcMask = { ...currentState.funcMask };
    }
  }

  addEventLog("系统", `登录成功: 工号 ${data.agentId}`, "LOGIN_SUCCESS");
  
  // 调用原始处理函数完成其他逻辑
  handleLoginSuccess(data);
};

// 状态变更处理
const handleStateChange = (state: any) => {
  console.log('收到状态变更事件:', state);
  
  // 直接更新当前组件的状态
  agentStatus.state = state.state;
  agentStatus.stateDesc = state.stateDesc || getStateDescription(state.state);
  agentStatus.funcMask = { ...state.funcMask };

  // 更新话机信息(如果事件中包含)
  if (state.phone) {
    agentInfo.phone = state.phone;
  }

  // 如果登录成功，但话机仍然为空，尝试从全局状态获取
  if (isLoggedIn.value && !agentInfo.phone) {
    // 尝试从GlobalStateManager获取
    const phone = globalStateManager.getPhone ? globalStateManager.getPhone() : localStorage.getItem('phone');
    if (phone) {
      agentInfo.phone = phone;
    }
  }

  // 记录状态变更日志
  addEventLog(
    "状态",
    `座席状态变更: ${agentStatus.stateDesc} (${agentStatus.state})`,
    "STATE_CHANGED"
  );
  
  // 更新isLoggedIn状态，确保与全局状态保持一致
  isLoggedIn.value = globalStateManager.isLoggedIn();
  
  // 如果登录成功，确保agentInfo有值
  if (isLoggedIn.value && !agentInfo.agentId) {
    agentInfo.agentId = globalStateManager.getAgentId();
  }
};

// 获取状态描述的辅助函数
const getStateDescription = (state: string): string => {
  const stateMap: Record<string, string> = {
    IDLE: "空闲",
    READY: "就绪",
    BUSY: "忙碌",
    TALK: "通话中",
    ALERTING: "振铃中",
    HELD: "保持中",
    OFFLINE: "离线",
    ACW: "话后处理",
    LOGOFF: "未登录"
  };

  return stateMap[state] || state;
};

// 登录成功处理
const handleLoginSuccess = (data: any) => {
  // isLoggedIn值将通过globalStateManager更新
  agentInfo.agentId = data.agentId;
  agentInfo.phone = data.phone;

  addEventLog("系统", `登录成功: 工号 ${data.agentId}`, "LOGIN_SUCCESS");
};

// 登出处理
const handleLogout = () => {
  // isLoggedIn值将通过globalStateManager更新
  agentInfo.agentId = "";
  agentInfo.phone = "";
  agentStatus.state = AgentStateType.LOGOFF;
  agentStatus.stateDesc = "未登录";
  agentStatus.funcMask = {};

  addEventLog("系统", "已登出", "LOGOUT_SUCCESS");
};

// 呼叫事件处理
const handleCallEvent = (event: any) => {
  addEventLog(event.type, event.content, "CALL_EVENT");
};

// 初始化完成处理
const handleInitComplete = (data: any) => {
  console.log("初始化完成", data.skills);
  if (data && data.skills && Array.isArray(data.skills)) {
    skills.value = data.skills;
  }
  addEventLog("系统", "CCBar初始化完成", "INIT_COMPLETE");
};

// 添加事件日志
const addEventLog = (type: string, content: string, id: string = "") => {
  const now = new Date();
  const time = `${now.getHours().toString().padStart(2, "0")}:${now
    .getMinutes()
    .toString()
    .padStart(2, "0")}:${now.getSeconds().toString().padStart(2, "0")}`;

  // 构建事件内容，如果提供了ID则添加
  const eventContent = id ? `[ID:${id}] ${content}` : content;

  eventLogs.value.unshift({ time, type, content: eventContent });

  // 限制最多显示20条
  if (eventLogs.value.length > 20) {
    eventLogs.value.pop();
  }
};

// 监听全局状态变更
const setupStateListeners = () => {
  // 监听状态变更
  globalStateManager.onStateChanged((event) => {
    console.log('全局状态变更事件:', event);
    
    // 更新登录状态
    isLoggedIn.value = globalStateManager.isLoggedIn();
    console.log('状态变更后的登录状态:', isLoggedIn.value);

    // 更新状态信息
    const currentState = event.currentState || globalStateManager.getState();
    if (currentState) {
      console.log('更新状态为:', currentState);
      
      // 确保类型正确
      agentStatus.state = currentState.state as AgentStateType;
      agentStatus.stateDesc = currentState.stateDesc || getStateDescription(currentState.state);
      agentStatus.funcMask = { ...currentState.funcMask };

      // 更新话机信息
      if (!agentInfo.phone && isLoggedIn.value) {
        // 尝试从GlobalStateManager获取
        const phone = globalStateManager.getPhone ? globalStateManager.getPhone() : localStorage.getItem('phone');
        if (phone) {
          agentInfo.phone = phone;
        }
      }

      addEventLog(
        "状态",
        `全局状态已变更: ${agentStatus.stateDesc} (${agentStatus.state})`,
        "GLOBAL_STATE_CHANGED"
      );
    }
  });

  // 使用watchEffect自动更新isLoggedIn
  watchEffect(() => {
    const loginState = globalStateManager.isLoggedIn();
    if (isLoggedIn.value !== loginState) {
      console.log(`自动同步登录状态: ${loginState}`);
      isLoggedIn.value = loginState;
      
      // 如果登出，重置状态
      if (!loginState) {
        agentStatus.state = AgentStateType.LOGOFF;
        agentStatus.stateDesc = "未登录";
        agentStatus.funcMask = {};
        agentInfo.agentId = "";
        agentInfo.phone = "";
      }
    }
  });
};

// 组件挂载时设置监听
onMounted(() => {
  setupStateListeners();

  // 初始化状态
  isLoggedIn.value = globalStateManager.isLoggedIn();
  console.log('组件挂载时登录状态:', isLoggedIn.value);

  if (isLoggedIn.value) {
    // 更新座席ID
    agentInfo.agentId = globalStateManager.getAgentId();
    
    // 更新话机信息
    if (!agentInfo.phone) {
      // 首先尝试从GlobalStateManager获取
      if (typeof globalStateManager.getPhone === 'function') {
        agentInfo.phone = globalStateManager.getPhone();
      }
      
      // 如果还为空，尝试从localStorage获取
      if (!agentInfo.phone) {
        agentInfo.phone = localStorage.getItem('phone') || '';
      }
    }
    
    // 获取并同步当前状态
    const state = globalStateManager.getState();
    console.log('组件挂载时获取全局状态:', state);
    
    // 确保类型正确
    if (state) {
      agentStatus.state = state.state as AgentStateType;
      agentStatus.stateDesc = state.stateDesc || getStateDescription(state.state);
      agentStatus.funcMask = { ...state.funcMask };
      
      // 记录日志
      addEventLog(
        "状态",
        `初始化座席状态: ${agentStatus.stateDesc} (${agentStatus.state})`,
        "INIT_STATE"
      );
    }
  } else {
    // 未登录状态，确保状态正确设置为LOGOFF
    agentStatus.state = AgentStateType.LOGOFF;
    agentStatus.stateDesc = "未登录";
  }

  addEventLog("系统", "组件已挂载", "COMPONENT_MOUNTED");
  
  // 强制触发一次状态更新，确保UI正确显示
  setTimeout(() => {
    const forceUpdate = { ...agentStatus };
    agentStatus.state = forceUpdate.state;
    agentStatus.stateDesc = forceUpdate.stateDesc;
  }, 100);
});

// 组件卸载时清理
onBeforeUnmount(() => {
  // 清理定时器
  if (stateCheckInterval !== null) {
    clearInterval(stateCheckInterval);
    stateCheckInterval = null;
  }

  addEventLog("系统", "组件已卸载", "COMPONENT_UNMOUNTED");
});
</script>

<style scoped>
.ccbar-example {
  font-family: Arial, sans-serif;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

h1 {
  text-align: center;
  margin-bottom: 30px;
  color: #303133;
}

.example-content {
  margin-top: 30px;
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 20px;
}

.status-card,
.event-card,
.integration-help {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.status-card h3,
.event-card h3,
.integration-help h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 10px;
}

.status-item {
  margin-bottom: 10px;
  display: flex;
}

.status-label {
  font-weight: bold;
  width: 80px;
  color: #606266;
}

.status-value {
  flex: 1;
}

.event-list {
  max-height: 300px;
  overflow-y: auto;
}

.no-events {
  color: #909399;
  text-align: center;
  padding: 20px 0;
}

.event-item {
  padding: 8px 0;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  align-items: flex-start;
}

.event-time {
  color: #909399;
  font-size: 12px;
  width: 60px;
}

.event-type {
  background-color: #409eff;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  margin-right: 10px;
  font-size: 10px;
  min-width: 60px;
  text-align: center;
}

.event-content {
  flex: 1;
  font-size: 13px;
}

.integration-help {
  margin-top: 20px;
}

.integration-help pre {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 15px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.5;
  color: #303133;
}

.feature-card {
  grid-column: 1 / -1;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.feature-item {
  padding: 10px;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.feature-title {
  font-weight: bold;
  margin-bottom: 5px;
  color: #409eff;
}

.feature-desc {
  color: #606266;
  font-size: 14px;
}

@media screen and (max-width: 768px) {
  .example-content {
    grid-template-columns: 1fr;
  }
}
</style>
