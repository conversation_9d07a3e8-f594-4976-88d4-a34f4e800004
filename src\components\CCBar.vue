<!--
CCBar.vue - 综合性话务条组件

这是一个综合性话务条组件，集成了以下功能为一体：
1. 轻量级工具条功能 - 基于CCBarToolbar
2. 登录弹窗功能 - 基于CCBarLogin
3. 转接弹窗功能 - 基于TransferCallDialog

该组件为第三方应用提供一站式的呼叫中心话务功能集成方案。
通过整合多个子组件，实现完整的话务功能协调。
-->

<template>
  <div class="ccbar-integrated">
    <!-- 工具条组件 -->
    <cc-bar-toolbar
      :config="config"
      :show-display-number="showDisplayNumber"
      :service-type="serviceType"
      @show-login="showLoginDialog = true"
      @state-changed="handleStateChange"
      @call-event="handleCallEvent"
      @transfer-request="transferDialogVisible = true"
      @init-complete="handleInitComplete"
    />
    
    <!-- 登录弹窗组件 -->
    <cc-bar-login
      :config="config"
      :visible="showLoginDialog"
      :skills="skills"
      @close="hideLoginDialog"
      @login-success="handleLoginSuccess"
    />
    
    <!-- 转接通话弹窗组件 -->
    <transfer-call-dialog
      v-model="transferDialogVisible"
      :current-call="currentCall"
      :show-display-number="showDisplayNumber"
      @transfer="handleTransfer"
    />
    
    <!-- 呼入提醒弹窗组件 -->
    <incoming-call-alert 
      :show="showIncomingCallAlert" 
      :callInfo="incomingCallInfo"
      @answer="handleAnswerCall"
      @reject="handleRejectCall"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import { CCBarService } from '../core/CCBarService';
import { CallEventType } from '../types';
import type { CallEvent } from '../types';

// 导入子组件
import { default as TransferCallDialog } from './TransferCallDialog.vue';
import { default as IncomingCallAlert } from './IncomingCallAlert.vue';

// 定义组件属性
const props = defineProps({
  config: {
    type: Object,
    required: true
  },
  showDisplayNumber: {
    type: Boolean,
    default: false
  },
  serviceType: {
    type: String,
    default: 'websocket',
    validator: (value: string) => ['websocket', 'polling'].includes(value)
  },
});

// 定义组件事件
const emit = defineEmits(['state-changed', 'login', 'logout', 'call-connected', 'call-disconnected', 'call-rejected', 'transfer']);
// 获取服务实例
const ccbarService = CCBarService.getInstance(props.config);

// 登录对话框状态
const showLoginDialog = ref(false);

// 转接对话框状态
const transferDialogVisible = ref(false);

// 呼入提醒弹窗状态
const showIncomingCallAlert = ref(false);
const incomingCallInfo = ref({
  phoneNumber: '',
  customerName: '',
  time: '',
  notes: ''
});

// 技能组列表，从工具条组件初始化时获取
const skills = ref<any[]>([]);

// 当前通话信息
const currentCall = ref({
  caller: '',
  called: '',
  custPhone: '',
  displayCustPhone: ''
});

// 处理工具条初始化完成事件
const handleInitComplete = (data: any) => {
  console.log("CCBar: 工具条初始化完成", data.skills);
  if (data && data.skills && Array.isArray(data.skills)) {
    skills.value = data.skills;
  }
};

// 显示/隐藏登录对话框
const hideLoginDialog = () => {
  showLoginDialog.value = false;
};

// 登录成功处理
const handleLoginSuccess = (data: any) => {
  hideLoginDialog();
  
  // 确保话机信息被正确传递
  if (data && !data.phone && ccbarService) {
    // 尝试从service获取话机信息
    const agentInfo = ccbarService.getAgentInfo();
    if (agentInfo && agentInfo.phone) {
      data.phone = agentInfo.phone;
    }
  }
  
  // 同时也保存到localStorage以便后续使用
  if (data && data.phone) {
    localStorage.setItem('phone', data.phone);
  }
  
  emit('login', data);
};

// 状态变化处理
const handleStateChange = (data: any) => {
  emit('state-changed', data);
};

// 呼叫事件处理
const handleCallEvent = (data: any) => {
  // 根据事件类型发送不同的事件
  if (data.type === CallEventType.CONNECTED) {
    currentCall.value = {
      caller: data.event.caller,
      called: data.event.called,
      custPhone: data.event.custPhone,
      displayCustPhone: data.event.displayCustPhone
    };
    emit('call-connected', data);
  } else if (data.type === CallEventType.DISCONNECTED) {
    emit('call-disconnected', data);
  }
};

// 转接处理
const handleTransfer = async (transferInfo: any) => {
  await ccbarService.transferCall(transferInfo);
  emit('transfer', transferInfo);
};

// 处理应答
const handleAnswerCall = async () => {
  await ccbarService.answerCall();
  showIncomingCallAlert.value = false;
};

// 处理拒接
const handleRejectCall = async () => {
  showIncomingCallAlert.value = false;
  try {
    await ccbarService.clearCall();
    emit('call-rejected', { 
      phoneNumber: incomingCallInfo.value.phoneNumber,
      time: new Date().toLocaleTimeString()
    });
  } catch (error) {
    console.error('拒接来电失败', error);
  }
};

// 初始化
onMounted(() => {
  // 监听振铃事件
  ccbarService.on(CallEventType.ALERTING, (event: unknown, callEvent: CallEvent) => {
    // 显示呼入提醒弹窗
    const now = new Date();
    const timeStr = now.toLocaleTimeString();
    
    incomingCallInfo.value = {
      phoneNumber: callEvent.event.displayCustPhone || callEvent.event.custPhone,
      customerName: callEvent.event.userData?.custName || '',
      time: timeStr,
      notes: callEvent.event.userData?.customData || ''
    };
    
    showIncomingCallAlert.value = true;
  });
  
  // 当呼叫连接或挂断时，隐藏弹窗
  ccbarService.on(CallEventType.CONNECTED, () => {
    showIncomingCallAlert.value = false;
  });
  
  ccbarService.on(CallEventType.DISCONNECTED, () => {
    showIncomingCallAlert.value = false;
  });
});

// 监听配置变化，更新服务实例
watch(() => props.config, (newConfig) => {
  if (newConfig && newConfig.baseURL) {
    CCBarService.getInstance(newConfig);
  }
}, { deep: true });
</script>

<style scoped>
.ccbar-integrated {
  width: 100%;
  position: relative;
}
</style>
