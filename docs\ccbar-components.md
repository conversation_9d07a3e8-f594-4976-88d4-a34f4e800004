# CCBar 话务条组件使用文档

本文档介绍如何在第三方应用中集成和使用 CCBar 话务条组件，实现呼叫中心座席的登录和通话控制功能。

## 组件介绍

CCBar 组件套件包括以下核心组件：

1. **CCBarToolbar 话务条组件**：提供座席状态显示和控制、呼叫控制等功能
2. **CCBarLogin 登录组件**：提供座席登录界面和功能

这些组件基于 Vue 3 开发，使用 TypeScript 确保类型安全，可以轻松集成到任何基于 Vue 3 的项目中。

## 安装和集成

### 1. 复制组件文件

将以下文件复制到您的项目中：

- `src/components/CCBarToolbar.vue`
- `src/components/CCBarLogin.vue`
- 相关的工具类和服务类（如 `core/CCBarService.ts` 等）

### 2. 配置服务参数

创建 CCBar 配置对象，示例：

```javascript
const ccbarConfig = {
  baseURL: '/api',  // API基础地址
  wsURL: '/ws',     // WebSocket地址
  entId: '123456',  // 企业ID
  loginKey: 'xxxxxxxxxx', // 登录密钥
  productId: '100000'  // 产品ID
};
```

### 3. 在页面中使用组件

在您的 Vue 组件中引入和使用这些组件：

```vue
<template>
  <div>
    <!-- 话务条组件 -->
    <CCBarToolbar 
      :config="ccbarConfig" 
      @show-login="showLoginDialog = true"
      @state-changed="handleStateChanged"
      @call-event="handleCallEvent"
      @logout-success="handleLogout"
    />
    
    <!-- 登录对话框 -->
    <CCBarLogin
      :config="ccbarConfig"
      :visible="showLoginDialog"
      @close="showLoginDialog = false"
      @login-success="handleLoginSuccess"
    />
  </div>
</template>

<script setup>
import * as CCBarToolbar from '@/components/CCBarToolbar.vue';
import * as CCBarLogin from '@/components/CCBarLogin.vue';
import { ref } from 'vue';

// CCBar配置
const ccbarConfig = {
  baseURL: '/api',
  wsURL: '/ws',
  entId: '123456',
  loginKey: 'xxxxxxxxxx',
  productId: '100000'
};

// 状态变量
const showLoginDialog = ref(false);

// 事件处理
const handleLoginSuccess = (data) => {
  // 登录成功处理
  console.log('登录成功:', data);
};

const handleStateChanged = (state) => {
  // 状态变更处理
  console.log('状态变更:', state);
};

const handleCallEvent = (event) => {
  // 呼叫事件处理
  console.log('呼叫事件:', event);
};

const handleLogout = () => {
  // 登出处理
  console.log('已登出');
};
</script>
```

## CCBarToolbar 组件

### 属性

| 属性名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|-----|-------|------|
| config | Object | 是 | - | CCBar服务配置对象，包含API地址、WebSocket地址等 |

### 事件

| 事件名 | 说明 | 回调参数 |
|-------|------|---------|
| show-login | 用户点击登录按钮时触发 | - |
| state-changed | 座席状态变更时触发 | state: 新的状态对象 |
| call-event | 呼叫相关事件触发 | event: 事件对象，包含type和content |
| logout-success | 登出成功时触发 | - |

### 功能说明

1. **座席状态显示**：显示当前座席的状态（空闲、忙碌、通话中等）
2. **状态持续时间**：显示当前状态持续了多长时间（秒/分/小时）
2. **状态控制**：提供置闲、置忙、特殊状态等按钮
3. **呼叫控制**：提供拨号、挂断、保持、恢复等呼叫控制功能
4. **事件通知**：显示最新的事件通知
5. **折叠/展开**：可以折叠为迷你模式，减少界面占用

## CCBarLogin 组件

### 属性

| 属性名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|-----|-------|------|
| config | Object | 是 | - | CCBar服务配置对象 |
| visible | Boolean | 否 | false | 控制对话框的显示/隐藏 |

### 事件

| 事件名 | 说明 | 回调参数 |
|-------|------|---------|
| close | 对话框关闭时触发 | - |
| login-success | 登录成功时触发 | data: 登录成功的数据，包含agentId和phone |

### 功能说明

1. **基本登录**：提供工号、密码、话机号输入
2. **高级选项**：可展开显示企业ID、产品ID、工作模式等高级配置
3. **错误提示**：显示登录过程中的错误信息
4. **加载状态**：显示登录过程中的加载状态

## 集成示例

完整的集成示例请参考 `src/examples/CCBarExample.vue` 文件，该示例展示了如何：

1. 配置和使用话务条组件
2. 处理各种事件
3. 集成登录对话框
4. 显示状态和事件日志

## 注意事项

1. 话务条默认为签出状态（未登录状态），需要用户先登录才能使用完整功能
2. 状态持续时间会在状态变更、登录或登出时自动重置
3. 确保正确配置API地址和WebSocket地址，这些地址需要指向CCBar服务器
4. 如果API请求跨域，需要配置适当的代理
5. 如果需要自定义样式，可以通过CSS覆盖组件的默认样式

## 支持的浏览器

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## 常见问题

**Q: 登录后话务条不显示状态?**
A: 检查WebSocket连接是否正常，以及服务器端是否正确发送状态更新事件。

**Q: 按钮点击无反应?**
A: 检查按钮是否被禁用，某些功能只有在特定状态下才能使用。 