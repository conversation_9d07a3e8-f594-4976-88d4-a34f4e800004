import { WebSocketConfig } from '../types/api';
import { EventEmitter } from '../core/EventEmitter';

/**
 * WebSocket服务类
 * 负责管理WebSocket连接、重连逻辑、心跳机制和消息处理
 */
export class WebSocketService {
  private ws: WebSocket | null = null;
  private config: WebSocketConfig;
  private eventEmitter: EventEmitter;
  private reconnectAttempts: number = 0;
  private reconnectTimer: number | null = null;
  private heartbeatTimer: number | null = null;
  private isConnecting: boolean = false;
  private isConnected: boolean = false;
  private static instance: WebSocketService;

  private constructor(config: WebSocketConfig) {
    this.config = this.getDefaultConfig(config);
    this.eventEmitter = EventEmitter.getInstance();
  }

  /**
   * 获取WebSocketService单例
   * @param config WebSocket配置
   */
  public static getInstance(config: WebSocketConfig): WebSocketService {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService(config);
    } else if (config.url !== WebSocketService.instance.config.url) {
      // 如果URL变化，先关闭现有连接，再重新创建实例
      WebSocketService.instance.close();
      WebSocketService.instance = new WebSocketService(config);
    }
    return WebSocketService.instance;
  }

  /**
   * 合并默认配置
   * @param config 用户配置
   */
  private getDefaultConfig(config: WebSocketConfig): WebSocketConfig {
    return {
      autoReconnect: true,
      maxReconnectAttempts: 10,
      reconnectInterval: 3000,
      heartbeatInterval: 30000,
      heartbeatMessage: { type: 'ping' },
      ...config
    };
  }

  /**
   * 连接WebSocket
   */
  public connect(): void {
    if (this.isConnected || this.isConnecting) {
      console.log('WebSocket已连接或正在连接中');
      return;
    }

    this.isConnecting = true;

    try {
      this.ws = new WebSocket(this.config.url);
      
      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onerror = this.handleError.bind(this);
    } catch (error) {
      this.isConnecting = false;
      console.error('WebSocket连接错误:', error);
      this.eventEmitter.emit('ws:error', error);
      this.attemptReconnect();
    }
  }

  /**
   * 处理连接成功
   * @param event 连接事件
   */
  private handleOpen(event: Event): void {
    this.isConnecting = false;
    this.isConnected = true;
    this.reconnectAttempts = 0;
    
    // 启动心跳
    this.startHeartbeat();
    
    console.log('WebSocket连接成功');
    this.eventEmitter.emit('ws:open', event);
    
    // 触发用户定义的回调
    this.config.onOpen?.(event);
  }

  /**
   * 处理连接关闭
   * @param event 关闭事件
   */
  private handleClose(event: CloseEvent): void {
    this.isConnecting = false;
    this.isConnected = false;
    
    // 停止心跳
    this.stopHeartbeat();
    
    console.log('WebSocket连接关闭', event.code, event.reason);
    this.eventEmitter.emit('ws:close', event);
    
    // 触发用户定义的回调
    this.config.onClose?.(event);
    
    // 尝试重连
    this.attemptReconnect();
  }

  /**
   * 处理接收消息
   * @param event 消息事件
   */
  private handleMessage(event: MessageEvent): void {
    let data;
    
    // 尝试解析JSON
    try {
      data = JSON.parse(event.data);
    } catch (error) {
      data = event.data;
    }
    
    // 处理心跳响应
    if (data && (data.type === 'pong' || data.heartbeat)) {
      this.eventEmitter.emit('ws:heartbeat', data);
      return;
    }
    
    // 处理会话超时
    if (data && (data.code === 'sessionTimeout' || data.message === '用户登录超时')) {
      console.warn('WebSocket会话超时');
      this.eventEmitter.emit('ws:sessionTimeout', data);
      this.config.onSessionTimeout?.();
      return;
    }
    
    // 发送消息事件
    this.eventEmitter.emit('ws:message', data);
    
    // 触发用户定义的回调
    this.config.onMessage?.(data);
  }

  /**
   * 处理连接错误
   * @param event 错误事件
   */
  private handleError(event: Event): void {
    console.error('WebSocket错误:', event);
    this.eventEmitter.emit('ws:error', event);
    
    // 触发用户定义的回调
    this.config.onError?.(event);
  }

  /**
   * 尝试重连
   */
  private attemptReconnect(): void {
    if (!this.config.autoReconnect) {
      return;
    }
    
    if (this.reconnectTimer !== null) {
      window.clearTimeout(this.reconnectTimer);
    }
    
    if (this.reconnectAttempts >= (this.config.maxReconnectAttempts || 10)) {
      console.error(`WebSocket重连失败，已达到最大重试次数(${this.config.maxReconnectAttempts})`);
      this.eventEmitter.emit('ws:reconnectFailed');
      return;
    }
    
    this.reconnectAttempts++;
    
    const delay = this.config.reconnectInterval || 3000;
    console.log(`尝试第${this.reconnectAttempts}次重连WebSocket，${delay}ms后...`);
    this.eventEmitter.emit('ws:reconnecting', { 
      attempt: this.reconnectAttempts, 
      maxAttempts: this.config.maxReconnectAttempts 
    });
    
    this.reconnectTimer = window.setTimeout(() => {
      this.connect();
    }, delay);
  }

  /**
   * 启动心跳机制
   */
  private startHeartbeat(): void {
    if (!this.config.heartbeatInterval) {
      return;
    }
    
    if (this.heartbeatTimer !== null) {
      window.clearInterval(this.heartbeatTimer);
    }
    
    this.heartbeatTimer = window.setInterval(() => {
      this.sendHeartbeat();
    }, this.config.heartbeatInterval);
  }

  /**
   * 停止心跳机制
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer !== null) {
      window.clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  /**
   * 发送心跳消息
   */
  private sendHeartbeat(): void {
    if (!this.isConnected || !this.ws) {
      return;
    }
    
    try {
      const message = typeof this.config.heartbeatMessage === 'string'
        ? this.config.heartbeatMessage
        : JSON.stringify(this.config.heartbeatMessage);
      
      this.ws.send(message);
      this.eventEmitter.emit('ws:heartbeatSent', this.config.heartbeatMessage);
    } catch (error) {
      console.error('发送心跳消息失败:', error);
    }
  }

  /**
   * 发送消息
   * @param data 消息数据
   */
  public send(data: any): boolean {
    if (!this.isConnected || !this.ws) {
      console.error('WebSocket未连接，无法发送消息');
      return false;
    }
    
    try {
      const message = typeof data === 'string' ? data : JSON.stringify(data);
      this.ws.send(message);
      return true;
    } catch (error) {
      console.error('发送WebSocket消息失败:', error);
      this.eventEmitter.emit('ws:sendError', { data, error });
      return false;
    }
  }

  /**
   * 关闭连接
   */
  public close(code?: number, reason?: string): void {
    if (!this.ws) {
      return;
    }
    
    this.isConnected = false;
    this.stopHeartbeat();
    
    if (this.reconnectTimer !== null) {
      window.clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    try {
      this.ws.close(code, reason);
    } catch (error) {
      console.error('关闭WebSocket连接失败:', error);
    }
  }

  /**
   * 重新连接
   */
  public reconnect(): void {
    this.close();
    this.connect();
  }

  /**
   * 是否已连接
   */
  public isWebSocketConnected(): boolean {
    return this.isConnected;
  }

  /**
   * 添加事件监听
   * @param event 事件名
   * @param callback 回调函数
   */
  public on(event: string, callback: Function): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听
   * @param event 事件名
   * @param callback 回调函数
   */
  public off(event: string, callback: Function): void {
    this.eventEmitter.off(event, callback);
  }

  /**
   * 添加一次性事件监听
   * @param event 事件名
   * @param callback 回调函数
   */
  public once(event: string, callback: Function): void {
    this.eventEmitter.once(event, callback);
  }
} 