<template>
  <div class="demo-container">
    <h1>CCBar 呼叫中心工具条演示</h1>
    
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <h2>功能演示</h2>
        </div>
      </template>
      
      <div class="demo-content">
        <div class="config-section">
          <h3>配置设置</h3>
          <el-form :model="config" label-width="120px" size="small">
            <el-form-item label="自动接收来电">
              <el-switch v-model="config.autoAnswer" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="initCCBar">初始化工具条</el-button>
            </el-form-item>
          </el-form>
        </div>
        
        <div class="ccbar-wrapper" v-if="initialized">
          <cc-bar :config="ccbarConfig" :showDisplayNumber="true" 
            @state-change="handleStateChange"
            @login="handleLogin"
            @logout="handleLogout"
            @call-connected="handleCallConnected"
            @call-disconnected="handleCallDisconnected"
            @call-rejected="handleCallRejected"
          />
        </div>
        
        <div class="events-section">
          <h3>事件日志</h3>
          <div class="events-wrapper">
            <div v-for="(event, index) in events" :key="index" class="event-item" :class="getEventClass(event.type)">
              <span class="event-time">{{ event.time }}</span>
              <span class="event-type">{{ event.type }}</span>
              <span class="event-message">{{ event.message }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-card>
    
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <h2>使用指南</h2>
        </div>
      </template>
      
      <div class="guide-content">
        <h3>演示说明</h3>
        <p>本演示使用模拟服务来展示CCBar的功能，无需真实的呼叫中心后台。</p>
        
        <h4>基本使用流程</h4>
        <ol>
          <li>点击"初始化工具条"按钮启动工具条</li>
          <li>使用任意用户名和密码登录（只要不为空即可）</li>
          <li>登录后，工具条会显示各种功能按钮</li>
          <li>如果启用了"自动接收来电"，系统会随机生成来电</li>
          <li>可以手动拨号、应答、挂断、置忙、置闲等</li>
          <li>所有操作和状态变更都会在事件日志中显示</li>
        </ol>
        
        <h4>快捷演示操作</h4>
        <div class="quick-demo-buttons">
          <el-button size="small" type="primary" @click="initWithSampleData">一键演示</el-button>
          <el-button size="small" @click="triggerIncomingCall" :disabled="!initialized">模拟来电</el-button>
          <el-button size="small" @click="makeOutgoingCall" :disabled="!initialized">模拟外呼</el-button>
          <el-button size="small" @click="showTransferDemo" :disabled="!initialized">模拟转接</el-button>
        </div>
        
        <h4>演示账号</h4>
        <p>登录时可使用以下示例账号：</p>
        <code>用户名: agent001<br>密码: 123456<br>分机: 8001</code>
        
        <h4>功能说明</h4>
        <ul>
          <li><strong>拨号</strong>：可以拨打任意号码，支持设置外显号码</li>
          <li><strong>应答</strong>：接听来电，开始通话</li>
          <li><strong>挂机</strong>：结束当前通话</li>
          <li><strong>置闲</strong>：将坐席状态设置为空闲，可以接收来电</li>
          <li><strong>置忙</strong>：将坐席状态设置为忙碌，暂停接收来电</li>
          <li><strong>保持</strong>：暂停当前通话，通话不会断开</li>
          <li><strong>恢复</strong>：恢复保持中的通话</li>
          <li><strong>模式</strong>：切换工作模式（呼入、呼出、智能外呼、自动）</li>
          <li><strong>转接</strong>：将当前通话转接给其他坐席或电话号码</li>
          <li><strong>完成转接</strong>：在咨询转接中完成转接操作</li>
          <li><strong>取消转接</strong>：在咨询转接中取消转接操作</li>
        </ul>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, onMounted, nextTick } from 'vue';
import CCBar from '../components/CCBar.vue';
import { CCBarService } from '../core/CCBarService';
import { MockCCBarService } from '../services/MockCCBarService';

export default defineComponent({
  name: 'DemoApp',
  components: {
    CCBar
  },
  setup() {
    const initialized = ref(false);
    const config = reactive({
      autoAnswer: true
    });
    
    // 使用环境变量配置
    const ccbarConfig = reactive({
      // TODO: [真实接口对接] 使用环境变量或实际API地址
      baseURL: import.meta.env.VITE_API_BASE_URL || 'https://mock-api.example.com',
      wsURL: import.meta.env.VITE_WS_URL || 'wss://mock-ws.example.com',
      timeout: parseInt(import.meta.env.VITE_REQUEST_TIMEOUT || '5000'),
      debug: import.meta.env.VITE_DEBUG === 'true',
      autoAnswer: import.meta.env.VITE_AUTO_ANSWER === 'true'
    });
    
    const events = ref<Array<{time: string, type: string, message: string}>>([]);
    
    // 记录事件
    const logEvent = (type: string, message: string) => {
      const now = new Date();
      const timeStr = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
      
      events.value.unshift({
        time: timeStr,
        type,
        message
      });
      
      // 保留最近的50条记录
      if (events.value.length > 50) {
        events.value.pop();
      }
    };
    
    // 初始化工具条
    const initCCBar = async () => {
      try {
        // 更新配置
        ccbarConfig.autoAnswer = config.autoAnswer;
        
        console.log('初始化 CCBar 工具条...');
        // 使用模拟服务（确保它已被初始化）
        const mockService = MockCCBarService.getInstance(ccbarConfig);
        console.log('MockCCBarService 初始化完成');
        
        // 设置初始化标志
        initialized.value = true;
        
        // 记录初始化事件
        logEvent('初始化', '工具条已初始化');
        
        // 等待下一个渲染周期，确保组件已挂载
        await nextTick();
        
        console.log('CCBar 工具条初始化状态：', initialized.value);
        console.log('CCBar 配置：', ccbarConfig);
      } catch (error) {
        console.error('初始化 CCBar 发生错误:', error);
        logEvent('错误', '初始化工具条失败');
      }
    };
    
    // 事件处理函数
    const handleStateChange = (state: any) => {
      logEvent('状态变更', `坐席状态: ${state.stateDesc}, 工作模式: ${state.workMode}`);
    };
    
    const handleLogin = () => {
      logEvent('登录', '坐席登录成功');
    };
    
    const handleLogout = () => {
      logEvent('登出', '坐席已登出');
    };
    
    const handleCallConnected = (callEvent: any) => {
      logEvent('呼叫建立', `与 ${callEvent.event.displayCustPhone} 的通话已建立`);
    };
    
    const handleCallDisconnected = (callEvent: any) => {
      const reason = callEvent.clearCause || '通话结束';
      logEvent('呼叫结束', `与 ${callEvent.event.displayCustPhone} 的通话已结束: ${reason}`);
    };
    
    // 处理拒接来电事件
    const handleCallRejected = (rejectInfo: any) => {
      logEvent('拒接来电', `拒接了来自 ${rejectInfo.phoneNumber} 的来电`);
    };
    
    // 根据事件类型获取CSS类名
    const getEventClass = (type: string) => {
      switch (type) {
        case '登录':
        case '呼叫建立':
          return 'event-success';
        case '登出':
        case '呼叫结束':
          return 'event-warning';
        case '状态变更':
          return 'event-info';
        case '错误':
          return 'event-error';
        default:
          return '';
      }
    };
    
    // 快捷演示功能
    const initWithSampleData = () => {
      ccbarConfig.autoAnswer = config.autoAnswer;
      initialized.value = true;
      
      // 记录初始化事件
      logEvent('初始化', '工具条已初始化，自动填充登录信息');
      
      // 使用模拟服务
      const mockService = MockCCBarService.getInstance(ccbarConfig);
      
      // 自动填充登录表单
      setTimeout(() => {
        const loginForm = document.querySelector('input[placeholder="请输入工号"]') as HTMLInputElement;
        const passwordForm = document.querySelector('input[placeholder="请输入密码"]') as HTMLInputElement;
        const phoneForm = document.querySelector('input[placeholder="请输入分机"]') as HTMLInputElement;
        const loginButton = document.querySelector('.ccbar-login-form .el-button') as HTMLButtonElement;
        
        if (loginForm && passwordForm && phoneForm && loginButton) {
          loginForm.value = 'agent001';
          loginForm.dispatchEvent(new Event('input'));
          
          passwordForm.value = '123456';
          passwordForm.dispatchEvent(new Event('input'));
          
          phoneForm.value = '8001';
          phoneForm.dispatchEvent(new Event('input'));
          
          // 延迟点击登录按钮
          setTimeout(() => {
            loginButton.click();
            logEvent('演示', '自动登录成功，请等待系统生成随机来电');
          }, 1000);
        }
      }, 500);
    };
    
    const triggerIncomingCall = () => {
      logEvent('演示', '正在模拟来电...');
      // 通过MockCCBarService静态方法触发来电
      MockCCBarService.triggerDemoIncomingCall();
    };
    
    const makeOutgoingCall = () => {
      logEvent('演示', '正在模拟外呼...');
      // 通过MockCCBarService静态方法触发外呼
      MockCCBarService.triggerDemoOutgoingCall();
    };
    
    const showTransferDemo = () => {
      logEvent('演示', '正在模拟转接...');
      // 通过MockCCBarService静态方法触发转接
      MockCCBarService.triggerDemoTransfer();
    };
    
    onMounted(() => {
      logEvent('系统', '演示应用已启动，请点击"初始化工具条"按钮开始');
    });
    
    return {
      initialized,
      config,
      ccbarConfig,
      events,
      initCCBar,
      handleStateChange,
      handleLogin,
      handleLogout,
      handleCallConnected,
      handleCallDisconnected,
      handleCallRejected,
      getEventClass,
      initWithSampleData,
      triggerIncomingCall,
      makeOutgoingCall,
      showTransferDemo
    };
  }
});
</script>

<style scoped>
.demo-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.demo-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.demo-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.config-section {
  margin-bottom: 20px;
}

.ccbar-wrapper {
  margin-bottom: 20px;
}

.events-section {
  margin-top: 20px;
}

.events-wrapper {
  height: 300px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  background-color: #f8f8f8;
}

.event-item {
  padding: 8px;
  margin-bottom: 6px;
  border-radius: 4px;
  background-color: #fff;
  border-left: 4px solid #909399;
}

.event-success {
  border-left-color: #67c23a;
}

.event-info {
  border-left-color: #409eff;
}

.event-warning {
  border-left-color: #e6a23c;
}

.event-error {
  border-left-color: #f56c6c;
}

.event-time {
  font-size: 12px;
  color: #909399;
  margin-right: 10px;
}

.event-type {
  font-weight: bold;
  margin-right: 10px;
}

.event-message {
  color: #606266;
}

.guide-content {
  line-height: 1.6;
}

ol, ul {
  padding-left: 20px;
}

h3, h4 {
  margin-top: 20px;
  margin-bottom: 10px;
}

.quick-demo-buttons {
  margin: 15px 0;
  display: flex;
  gap: 10px;
}

code {
  display: block;
  background-color: #f8f8f8;
  padding: 10px;
  border-radius: 4px;
  margin: 10px 0;
  border-left: 4px solid #409eff;
  font-family: monospace;
  line-height: 1.6;
}
</style> 