/**
 * 连接管理模块
 * 负责座席登录、登出和会话维护
 */

import { IConnectionManager, IEventManager } from '../interfaces';
import { LoginParams, CCBarResponse, CCBarConfig, AgentStateType } from '../../types';
import { RequestService } from '../../utils/request';
import { WebSocketService } from '../../utils/websocket';
import { ccbarDebugger, hashMd5 } from '../../utils';
import { PollingService } from '../../utils/polling';

/**
 * 连接管理器实现类
 */
export class ConnectionManager implements IConnectionManager {
  private requestService: RequestService;
  private wsService: WebSocketService | null;
  private pollingService: PollingService | null = null;
  private eventManager: IEventManager;
  private config: CCBarConfig;
  private isLogined: boolean = false;
  private sessionId: string = '';
  private agentInfo: {
    agentId: string;
    password: string;
    state: string;
    phoneType?: string;
    phone?: string;
    credential?: string;
  } = {
      agentId: '',
      password: '',
      state: AgentStateType.LOGOFF
    };

  /**
   * 构造函数
   * @param requestService HTTP请求服务
   * @param wsService WebSocket服务
   * @param eventManager 事件管理器
   * @param config 配置信息
   */
  constructor(
    requestService: RequestService,
    wsService: WebSocketService | null,
    eventManager: IEventManager,
    config: CCBarConfig
  ) {
    this.requestService = requestService;
    this.wsService = wsService;
    this.eventManager = eventManager;
    this.config = config;

    // 初始化轮询服务
    this.initPollingService();

    // 如果存在WebSocket服务，初始化WebSocket事件
    if (this.wsService) {
      this.initWebSocketEvents();
    }
  }

  /**
   * 初始化轮询服务
   */
  private initPollingService(): void {
    // 创建轮询服务
    this.pollingService = PollingService.getInstance({
      baseURL: this.config.baseURL || '',
      pollingInterval: this.config.pollingInterval || 10000,
    });

    if (this.pollingService) {
      // 设置会话超时处理
      this.pollingService.on('session:timeout', () => {
        this.eventManager.emit('session:timeout', {
          message: '会话已超时，请重新登录'
        });
      });

      // 监听系统停止轮询事件
      this.eventManager.on('system:stopPolling', (data: any) => {
        ccbarDebugger(`收到停止轮询指令: ${data.reason || 'Unknown reason'}`);

        // 确保轮询服务存在
        if (this.pollingService) {
          // 停止轮询
          ccbarDebugger('断开长轮询连接');
          this.pollingService.stopPolling();
          this.pollingService.setLoginState(false);
          this.pollingService.setSessionId(null);
          this.pollingService.setAgentId(null);

          // 通知其他组件轮询已停止
          this.eventManager.emit('polling:stopped', {
            reason: data.reason,
            timestamp: data.timestamp || new Date().getTime()
          });
        }
      });

      // 监听强制停止轮询事件（主要用于403错误）
      this.eventManager.on('polling:forceStop', (data: any) => {
        ccbarDebugger(`收到强制停止轮询指令: ${data.reason || '403错误'}`);

        // 确保轮询服务存在
        if (this.pollingService) {
          this.pollingService.stopPolling();
          this.pollingService.setLoginState(false);
          this.pollingService.setSessionId(null);
          this.pollingService.setAgentId(null);

          ccbarDebugger('轮询服务已被强制停止');

          // 通知其他组件轮询已停止
          this.eventManager.emit('polling:stopped', {
            reason: data.reason,
            timestamp: data.timestamp || new Date().getTime(),
            isForced: true
          });

          // 如果有心跳，也停止心跳
          if (this.wsService) {
            this.wsService.stopHeartbeat();
          }
        }
      });

      // 处理呼叫事件
      this.pollingService.on('call:incoming', (data: any) => {
        this.eventManager.emit('call:incoming', data);
      });

      this.pollingService.on('call:established', (data: any) => {
        this.eventManager.emit('call:established', data);
      });

      this.pollingService.on('call:ended', (data: any) => {
        this.eventManager.emit('call:ended', data);
      });

      // 处理状态变更事件
      this.pollingService.on('agent:stateChanged', (data: any) => {
        this.eventManager.emit('agent:stateChanged', data);
      });
    }
  }

  /**
   * 初始化WebSocket事件
   */
  private initWebSocketEvents(): void {
    if (!this.wsService) return;

    // 监听WebSocket消息
    this.wsService.on('message', (data: any) => {
      try {
        // 解析并处理来自WebSocket的各种事件
        if (data.event) {
          switch (data.event) {
            case 'callOffered': // 呼入事件
              this.eventManager.emit('call:incoming', data);
              break;
            case 'callEstablished': // 通话建立
              this.eventManager.emit('call:established', data);
              break;
            case 'callReleased': // 通话结束
              this.eventManager.emit('call:ended', data);
              break;
            case 'agentStateChanged': // 座席状态改变
              this.eventManager.emit('agent:stateChanged', data);
              break;
            case 'heartbeatResponse': // 心跳响应
              // 心跳响应不需要特殊处理
              break;
            default:
              // 其他事件直接转发
              this.eventManager.emit(`ws:${data.event}`, data);
              break;
          }
        }
      } catch (error) {
        ccbarDebugger('WebSocket消息处理错误', error, 'error');
      }
    });

    // 监听连接成功事件
    this.wsService.on('open', () => {
      ccbarDebugger('WebSocket连接成功');
      this.eventManager.emit('ws:connected');

      // 尝试使用心跳机制
      this.startHeartbeat();
    });

    // 监听连接关闭事件
    this.wsService.on('close', () => {
      ccbarDebugger('WebSocket连接关闭');
      this.eventManager.emit('ws:disconnected');

      // WebSocket断开时，尝试使用轮询
      this.startPolling();
    });

    // 监听连接错误事件
    this.wsService.on('error', (error: any) => {
      ccbarDebugger('WebSocket连接错误', error);
      this.eventManager.emit('ws:error', error);

      // WebSocket错误时，尝试使用轮询
      this.startPolling();
    });
  }

  /**
   * 座席登录
   * @param params 登录参数
   * @returns 登录结果
   */
  public async login(params: LoginParams): Promise<CCBarResponse> {
    try {
      // 保存登录信息
      this.agentInfo.agentId = params.username;
      this.agentInfo.password = params.password;
      this.agentInfo.phoneType = params.phoneType || 'softphone';
      this.agentInfo.phone = params.phone || '';
      this.agentInfo.credential = params.credential || '';

      // 构建登录请求参数 - 适配原始ccbar.js
      const loginParams = {
        data: {
          agentId: params.username,
          token: hashMd5(params.password), // 使用MD5加密密码
          phoneNum: params.phone || '',
          readyMode: params.readyMode || 'notReady',
          autoAnswer: params.autoAnswer || false,
          entId: this.config.entId || '',
          loginKey: this.config.loginKey || '',
          productId: this.config.productId || '',
          timestamp: new Date().getTime(),
          skillId: params.skillId || '',
          credential: params.credential || ''
        }
      };
      // 执行登录请求
      const result = await this.requestService.post('AgentEvent?action=login', loginParams, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8'
        }
      });

      // 处理登录结果
      if (result.state && (result.data?.code === 'succ' || result.data?.resultCode === '000')) {
        this.isLogined = true;
        sessionStorage.setItem('isLogined', this.isLogined.toString());
        this.agentInfo.state = AgentStateType.BUSY;

        // 保存session ID
        if (result.data.result && result.data.result.sessionId) {
          this.sessionId = result.data.result.sessionId;
        }
        // 根据配置选择通信方式
        localStorage.setItem('sessionId', this.sessionId);
        localStorage.setItem('agentId', result.data.result.agentId,);
        localStorage.setItem('cuid', result.data.result.cuid);
        this.startPolling();
        // 触发登录成功事件
        return {
          state: true,
          msg: '登录成功',
          data: {
            code: 'succ',
            content: '登录成功',
            result: {
              agentId: result.data.result.agentId,
              state: this.agentInfo.state,
              sessionId: this.sessionId
            }
          }
        };
      } else {
        return {
          state: false,
          msg: result.data?.content || '登录失败',
          data: {
            code: 'fail',
            content: result.data?.content || '登录失败',
            result: null
          }
        };
      }
    } catch (error: any) {
      ccbarDebugger('登录异常', error);
      return {
        state: false,
        msg: error.message || '登录异常',
        data: {
          code: 'error',
          content: error.message || '登录异常',
          result: null
        }
      };
    }
  }

  /**
   * 座席登出
   * @returns 登出结果
   */
  public async logout(): Promise<CCBarResponse> {
    if (sessionStorage.getItem('isLogined') && sessionStorage.getItem('isLogined') === 'false') {
      return {
        state: false,
        msg: '座席未登录',
        data: {
          code: 'fail',
          content: '座席未登录',
          result: null
        }
      };
    }

    try {
      // 构建登出请求参数
      const logoutParams = {
        data: {
          cmd: 'logout',
          agentId: this.agentInfo.agentId,
          sessionId: this.sessionId,
          event: 'agentlogout',
          messageId: "cmdLogout",
          cuid: localStorage.getItem('cuid') || '',
          rid: '',
          timestamp: new Date().getTime()
        }
      };

      // 停止轮询
      if (this.pollingService) {
        ccbarDebugger('断开长轮询连接');
        this.pollingService.stopPolling();
        this.pollingService.setLoginState(false);
        this.pollingService.setSessionId(null);
        this.pollingService.setAgentId(null);
      }

      // 关闭WebSocket连接
      if (this.wsService) {
        ccbarDebugger('断开WebSocket连接');
        this.wsService.close();

        // 停止WebSocket心跳
        this.wsService.stopHeartbeat();
      }

      // 执行登出请求
      const result = await this.requestService.post('AgentEvent?action=event', logoutParams, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8'
        }
      });

      // 重置状态
      this.isLogined = false;
      sessionStorage.setItem('isLogined', 'false');
      this.sessionId = '';
      this.agentInfo.state = AgentStateType.LOGOFF;

      // 清除本地存储
      localStorage.removeItem('sessionId');
      localStorage.removeItem('agentId');
      localStorage.removeItem('cuid');

      return {
        state: true,
        msg: '登出成功',
        data: {
          code: 'succ',
          content: '登出成功',
          result: null
        }
      };
    } catch (error: any) {
      ccbarDebugger('登出异常', error);
      return {
        state: false,
        msg: error.message || '登出异常',
        data: {
          code: 'error',
          content: error.message || '登出异常',
          result: null
        }
      };
    }
  }
  public async initCCbar(): Promise<CCBarResponse> {
    try {
      // 构建登出请求参数
      const initParams = {
        data: {
          timestamp: new Date().getTime()
        }
      };

      // 执行登出请求
      const result = await this.requestService.post('/AgentEvent?action=ccbarInit', initParams, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8'
        }
      });
      return {
        state: true,
        msg: '初始化成功',
        data: {
          code: 'succ',
          content: '初始化成功',
          result: result.data
        }
      };
    } catch (error: any) {
      ccbarDebugger('登出异常', error);
      return {
        state: false,
        msg: error.message || '登出异常',
        data: {
          code: 'error',
          content: error.message || '登出异常',
          result: null
        }
      };
    }
  }
  /**
   * 检查是否已登录
   * @returns 是否已登录
   */
  public isLoggedIn(): boolean {
    return sessionStorage.getItem('isLogined') === 'true';
  }

  /**
   * 检查是否已连接
   * @returns 是否已连接
   */
  public isConnected(): boolean {
    return this.wsService?.isConnected || false;
  }

  /**
   * 获取会话ID
   * @returns 会话ID
   */
  public getSessionId(): string {
    return this.sessionId;
  }

  /**
   * 获取座席信息
   * @returns 座席信息
   */
  public getAgentInfo(): any {
    return {
      agentId: this.agentInfo.agentId,
      state: this.agentInfo.state,
      phoneType: this.agentInfo.phoneType || 'softphone',
      phone: this.agentInfo.phone || ''
    };
  }

  /**
   * 设置登录状态
   * @param isLogined 登录状态
   */
  public setLoginState(isLogined: boolean): void {
    this.isLogined = isLogined;
    sessionStorage.setItem('isLogined', isLogined.toString());

    // 同步更新轮询服务的状态
    if (this.pollingService) {
      this.pollingService.setLoginState(isLogined);

      if (!isLogined) {
        // 如果登出，则停止轮询
        this.pollingService.stopPolling();
      }
    }

    // 通知事件管理器
    this.eventManager.emit('connection:state', {
      isLogined: isLogined,
      timestamp: new Date().getTime()
    });
  }

  /**
   * 启动WebSocket心跳
   */
  private startHeartbeat(): void {
    // 心跳实现
    if (this.wsService && this.wsService.isConnected) {
      this.wsService.startHeartbeat(30000); // 30秒心跳间隔
      ccbarDebugger('WebSocket心跳已启动');
    }
  }

  /**
   * 启动HTTP轮询
   */
  private startPolling(): void {
    // 确保用户已登录且轮询服务已初始化
    if (!this.pollingService || !this.isLogined) {
      ccbarDebugger('无法启动轮询：服务未初始化或用户未登录');
      return;
    }

    ccbarDebugger('开始HTTP长轮询');

    // 设置轮询参数
    this.pollingService.setSessionId(this.sessionId);
    this.pollingService.setAgentId(this.agentInfo.agentId);
    this.pollingService.setLoginState(this.isLogined);

    // 启动轮询
    this.pollingService.startPolling();

    ccbarDebugger('HTTP长轮询已启动');
  }
} 