import { EventEmitter } from '../core/EventEmitter';
import { PollingConfig } from '../types/api';
import { HttpService } from './http';

/**
 * HTTP轮询服务类
 * 用于通过HTTP轮询方式获取服务器事件，作为WebSocket的替代方案
 */
export class PollingService {
  private httpService: HttpService;
  private eventEmitter: EventEmitter;
  private config: PollingConfig;
  private pollingTimer: number | null = null;
  private isPolling: boolean = false;
  private static instance: PollingService;

  private constructor(config: PollingConfig) {
    this.config = this.getDefaultConfig(config);
    this.eventEmitter = EventEmitter.getInstance();
    this.httpService = HttpService.getInstance({
      baseURL: this.config.baseURL,
      timeout: this.config.timeout || 10000
    });
  }

  /**
   * 获取PollingService单例
   * @param config 轮询配置
   */
  public static getInstance(config: PollingConfig): PollingService {
    if (!PollingService.instance) {
      PollingService.instance = new PollingService(config);
    } else if (config.baseURL !== PollingService.instance.config.baseURL) {
      // 配置变更，重新创建实例
      PollingService.instance.stop();
      PollingService.instance = new PollingService(config);
    }
    return PollingService.instance;
  }

  /**
   * 合并默认配置
   * @param config 用户配置
   */
  private getDefaultConfig(config: PollingConfig): PollingConfig {
    return {
      baseURL: '',
      pollingUrl: '/events',
      pollingInterval: 5000,
      timeout: 10000,
      headers: {},
      params: {},
      ...config
    };
  }

  /**
   * 开始轮询
   */
  public start(): void {
    if (this.isPolling) {
      console.log('轮询已经启动');
      return;
    }

    this.isPolling = true;
    this.poll();
    console.log(`轮询已启动，间隔：${this.config.pollingInterval}ms`);
    this.eventEmitter.emit('polling:start');
  }

  /**
   * 执行单次轮询
   */
  private async poll(): Promise<void> {
    if (!this.isPolling) {
      return;
    }

    try {
      // TODO: [真实接口对接] 根据实际接口调整请求参数
      const response = await this.httpService.get(this.config.pollingUrl, {
        params: {
          timestamp: new Date().getTime(),
          ...this.config.params
        }
      });

      this.processResponse(response);
    } catch (error) {
      console.error('轮询请求失败:', error);
      this.eventEmitter.emit('polling:error', error);
      this.config.onError?.(error);
    } finally {
      // 设置下一次轮询
      if (this.isPolling) {
        this.pollingTimer = window.setTimeout(() => {
          this.poll();
        }, this.config.pollingInterval);
      }
    }
  }

  /**
   * 处理轮询响应数据
   * @param response 响应数据
   */
  private processResponse(response: any): void {
    // 检查会话超时
    if (response && (response.code === 'sessionTimeout' || response.message === '用户登录超时')) {
      console.warn('轮询会话超时');
      this.eventEmitter.emit('polling:sessionTimeout', response);
      this.config.onSessionTimeout?.();
      return;
    }

    if (response && response.success) {
      const events = response.data || [];
      
      if (events.length > 0) {
        // 发送新事件
        this.eventEmitter.emit('polling:events', events);
        this.config.onEvents?.(events);
        
        // 处理不同类型的事件
        events.forEach((event: any) => {
          // 根据事件类型分发不同的事件
          if (event.type) {
            this.eventEmitter.emit(`polling:event:${event.type}`, event);
            
            // 针对特定类型事件的处理
            switch (event.type) {
              case 'callIncoming':
                this.eventEmitter.emit('call:incoming', event);
                break;
              case 'callEnded':
                this.eventEmitter.emit('call:ended', event);
                break;
              case 'agentStateChanged':
                this.eventEmitter.emit('agent:stateChanged', event);
                break;
              // 可以根据需要添加更多类型的事件处理
            }
          }
        });
      }
    }
    
    // 发送响应事件
    this.eventEmitter.emit('polling:response', response);
    this.config.onResponse?.(response);
  }

  /**
   * 停止轮询
   */
  public stop(): void {
    if (!this.isPolling) {
      return;
    }

    this.isPolling = false;
    
    if (this.pollingTimer !== null) {
      window.clearTimeout(this.pollingTimer);
      this.pollingTimer = null;
    }
    
    console.log('轮询已停止');
    this.eventEmitter.emit('polling:stop');
  }

  /**
   * 重新开始轮询
   */
  public restart(): void {
    this.stop();
    this.start();
  }

  /**
   * 更新轮询参数
   * @param params 参数对象
   */
  public updateParams(params: Record<string, any>): void {
    this.config.params = {
      ...this.config.params,
      ...params
    };
    
    // 如果已经在轮询，则重启以使用新参数
    if (this.isPolling) {
      this.restart();
    }
  }

  /**
   * 更新轮询间隔
   * @param interval 间隔时间(ms)
   */
  public updateInterval(interval: number): void {
    if (interval < 1000) {
      console.warn('轮询间隔不应小于1000ms，设置为1000ms');
      interval = 1000;
    }
    
    this.config.pollingInterval = interval;
    
    // 如果已经在轮询，则重启以使用新间隔
    if (this.isPolling) {
      this.restart();
    }
  }
  
  /**
   * 是否正在轮询
   */
  public isPollingActive(): boolean {
    return this.isPolling;
  }

  /**
   * 添加事件监听
   * @param event 事件名
   * @param callback 回调函数
   */
  public on(event: string, callback: Function): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听
   * @param event 事件名
   * @param callback 回调函数
   */
  public off(event: string, callback: Function): void {
    this.eventEmitter.off(event, callback);
  }

  /**
   * 添加一次性事件监听
   * @param event 事件名
   * @param callback 回调函数
   */
  public once(event: string, callback: Function): void {
    this.eventEmitter.once(event, callback);
  }
} 