import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router';
import LoginDemo from '../demo/LoginDemo.vue';
import ConfigPage from '../demo/ConfigPage.vue';
import CCBarExample from '../examples/CCBarExample.vue';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'LoginDemo',
    component: LoginDemo,
    meta: {
      title: '签入演示 - CCBar'
    }
  },
  {
    path: '/config',
    name: 'ConfigPage',
    component: ConfigPage,
    meta: {
      title: '配置管理 - CCBar'
    }
  },
  {
    path: '/ccbar-demo',
    name: 'CCBarExample',
    component: CCBarExample,
    meta: {
      title: '话务条演示 - CCBar'
    }
  },
  {
    path: '/supervisor',
    name: 'SupervisorDemo',
    component: () => import('../views/SupervisorDemo.vue')
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/'
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

// 设置页面标题
router.beforeEach((to, from, next) => {
  document.title = to.meta.title as string || 'CCBar演示';
  next();
});

export default router; 